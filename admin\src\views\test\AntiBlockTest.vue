<template>
  <div class="anti-block-test">
    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <span>防红系统API测试</span>
          <el-button type="primary" @click="runAllTests">运行所有测试</el-button>
        </div>
      </template>

      <div class="test-section">
        <h3>1. 路由测试</h3>
        <el-space wrap>
          <el-button @click="testSimpleRoute" :loading="loading.simple">测试简单路由</el-button>
          <el-button @click="testPromotionRoute" :loading="loading.promotion">测试推广链接路由</el-button>
        </el-space>
      </div>

      <div class="test-section">
        <h3>2. 防红推广链接测试</h3>
        <el-form :model="testForm" label-width="120px">
          <el-form-item label="群组ID">
            <el-input v-model="testForm.groupId" placeholder="输入群组ID" style="width: 200px" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="testAntiBlockApi" :loading="loading.antiBlock">
              测试防红API
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <div class="test-section">
        <h3>3. 测试结果</h3>
        <el-scrollbar height="400px">
          <div class="test-results">
            <div v-for="(result, index) in testResults" :key="index" class="test-result-item">
              <div class="result-header">
                <el-tag :type="result.success ? 'success' : 'danger'">
                  {{ result.success ? '成功' : '失败' }}
                </el-tag>
                <span class="result-title">{{ result.title }}</span>
                <span class="result-time">{{ result.timestamp }}</span>
              </div>
              <div class="result-content">
                <pre>{{ JSON.stringify(result.data, null, 2) }}</pre>
              </div>
            </div>
          </div>
        </el-scrollbar>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { generateGroupPromotionLink } from '@/api/anti-block'
import api from '@/api'

const loading = reactive({
  simple: false,
  promotion: false,
  antiBlock: false
})

const testForm = reactive({
  groupId: '1'
})

const testResults = ref([])

const addTestResult = (title, success, data) => {
  testResults.value.unshift({
    title,
    success,
    data,
    timestamp: new Date().toLocaleTimeString()
  })
}

// 测试简单路由
const testSimpleRoute = async () => {
  loading.simple = true
  try {
    const response = await api.post(`/admin/test/simple/${testForm.groupId}`)
    addTestResult('简单路由测试', true, response.data)
    ElMessage.success('简单路由测试成功')
  } catch (error) {
    addTestResult('简单路由测试', false, {
      error: error.message,
      response: error.response?.data,
      status: error.response?.status
    })
    ElMessage.error('简单路由测试失败')
  } finally {
    loading.simple = false
  }
}

// 测试推广链接路由
const testPromotionRoute = async () => {
  loading.promotion = true
  try {
    const response = await api.post(`/admin/test/promotion-link/${testForm.groupId}`)
    addTestResult('推广链接路由测试', true, response.data)
    ElMessage.success('推广链接路由测试成功')
  } catch (error) {
    addTestResult('推广链接路由测试', false, {
      error: error.message,
      response: error.response?.data,
      status: error.response?.status
    })
    ElMessage.error('推广链接路由测试失败')
  } finally {
    loading.promotion = false
  }
}

// 测试防红API
const testAntiBlockApi = async () => {
  loading.antiBlock = true
  try {
    const response = await generateGroupPromotionLink(testForm.groupId, {
      enable_anti_block: true,
      enable_short_link: true,
      link_type: 'promotion'
    })
    addTestResult('防红推广链接API测试', true, response.data)
    ElMessage.success('防红API测试成功')
  } catch (error) {
    addTestResult('防红推广链接API测试', false, {
      error: error.message,
      response: error.response?.data,
      status: error.response?.status,
      config: error.config
    })
    ElMessage.error('防红API测试失败')
  } finally {
    loading.antiBlock = false
  }
}

// 运行所有测试
const runAllTests = async () => {
  await testSimpleRoute()
  await new Promise(resolve => setTimeout(resolve, 1000))
  await testPromotionRoute()
  await new Promise(resolve => setTimeout(resolve, 1000))
  await testAntiBlockApi()
}
</script>

<style scoped>
.anti-block-test {
  padding: 20px;
}

.test-card {
  max-width: 1200px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.test-section h3 {
  margin-top: 0;
  color: #303133;
}

.test-results {
  background: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
}

.test-result-item {
  background: white;
  margin-bottom: 10px;
  padding: 15px;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.result-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.result-title {
  font-weight: bold;
  color: #303133;
}

.result-time {
  color: #909399;
  font-size: 12px;
  margin-left: auto;
}

.result-content {
  background: #f8f9fa;
  padding: 10px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  overflow-x: auto;
}

.result-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
