import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import { resolve } from 'path'
import { visualizer } from 'rollup-plugin-visualizer'
import compression from 'vite-plugin-compression'
import { splitVendorChunkPlugin } from 'vite'

export default defineConfig(({ command, mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '')
  const isDev = command === 'serve'
  const isProd = command === 'build' && mode === 'production'

  return {
    root: '.',
    plugins: [
      vue({
        template: {
          compilerOptions: {
            // 生产环境移除注释
            comments: !isProd
          }
        }
      }),
      
      // 自动导入
      AutoImport({
        resolvers: [ElementPlusResolver()],
        imports: [
          'vue', 
          'vue-router', 
          'pinia',
          {
            '@/utils/lazyLoad': ['lazyLoad', 'preloadComponent', 'performanceMonitor'],
            '@/components/base': ['BaseDialog', 'BaseForm', 'BaseChart', 'BaseTable', 'BaseButton']
          }
        ],
        dts: 'auto-imports.d.ts',
        eslintrc: {
          enabled: true,
          filepath: './.eslintrc-auto-import.json',
          globalsPropValue: true
        }
      }),
      
      // 组件自动导入
      Components({
        resolvers: [ElementPlusResolver({
          importStyle: 'sass', // 按需导入样式
          directives: true,
          version: '2.4.0'
        })],
        dts: 'components.d.ts',
        dirs: [
          'src/components',
          'src/components/base',
          'src/components/common',
          'src/views'
        ],
        deep: true,
        directoryAsNamespace: false,
        include: [/\.vue$/, /\.vue\?vue/],
        exclude: [/[\\/]node_modules[\\/]/, /[\\/]\.git[\\/]/, /[\\/]\.nuxt[\\/]/]
      }),
      
      // 代码分割插件
      splitVendorChunkPlugin(),
      
      // 生产环境压缩
      ...(isProd ? [
        compression({
          algorithm: 'gzip',
          ext: '.gz',
          threshold: 1024, // 降低阈值，压缩更多文件
          deleteOriginFile: false,
          filter: /\.(js|css|html|svg|ico|txt)$/i
        }),
        compression({
          algorithm: 'brotliCompress',
          ext: '.br',
          threshold: 1024,
          deleteOriginFile: false,
          filter: /\.(js|css|html|svg|ico|txt)$/i
        }),
        visualizer({
          open: false,
          gzipSize: true,
          brotliSize: true,
          filename: 'dist/bundle-analysis.html',
          template: 'treemap' // 使用树状图更直观
        })
      ] : []),
      
      // 开发环境插件
      ...(isDev ? [
        // 开发时可以添加一些调试插件
      ] : [])
    ],
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
    },
  },
  base: process.env.NODE_ENV === 'development' ? '/' : '/admin/',
  server: {
    port: 3000,  // 修改为3000端口，避免与用户端冲突
    host: '0.0.0.0',
    hmr: {
      overlay: false // 关闭错误覆盖层，提升开发体验
    },
    // 强制启用代理
    proxy: {
      '/api': {
        target: 'http://localhost:8888',
        changeOrigin: true,
        secure: false,
        timeout: 10000,
        configure: (proxy, options) => {
          proxy.on('error', (err, req, res) => {
            console.log('代理错误:', err.message)
          })
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log('代理请求:', req.method, req.url)
          })
          proxy.on('proxyRes', (proxyRes, req, res) => {
            console.log('代理响应:', req.method, req.url, proxyRes.statusCode)
          })
        }
      },
    },
  },
  build: {
    outDir: '../public/admin',
    assetsDir: 'assets',
    sourcemap: false,
    target: 'esnext',
    minify: 'terser',
    emptyOutDir: true,
    terserOptions: {
      compress: {
        drop_console: process.env.NODE_ENV === 'production', // 生产环境移除console.log
        drop_debugger: true,
        pure_funcs: ['console.warn', 'console.info'],
      },
      format: {
        comments: false,
      },
    },
    rollupOptions: {
      output: {
        // 智能代码分割
        manualChunks: (id) => {
          // 将node_modules中的包分组
          if (id.includes('node_modules')) {
            // Element Plus 单独分包
            if (id.includes('element-plus')) {
              return 'element-plus'
            }
            
            // Vue 核心库
            if (id.includes('vue') || id.includes('pinia')) {
              return 'vue-vendor'
            }
            
            // ECharts 图表库
            if (id.includes('echarts')) {
              return 'echarts'
            }
            
            // 工具库
            if (id.includes('axios') || id.includes('dayjs') || id.includes('js-cookie')) {
              return 'utils'
            }
            
            // 其他第三方库
            return 'vendor'
          }
          
          // 将基础组件单独分包
          if (id.includes('src/components/base')) {
            return 'base-components'
          }
          
          // 将视图组件按模块分包
          if (id.includes('src/views')) {
            const viewPath = id.split('src/views/')[1]
            const module = viewPath.split('/')[0]
            return `views-${module}`
          }
        },
        
        // 资源文件命名
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name.split('.')
          let extType = info[info.length - 1]
          
          if (/\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/i.test(assetInfo.name)) {
            extType = 'media'
          } else if (/\.(png|jpe?g|gif|svg)(\?.*)?$/i.test(assetInfo.name)) {
            extType = 'images'
          } else if (/\.(woff2?|eot|ttf|otf)(\?.*)?$/i.test(assetInfo.name)) {
            extType = 'fonts'
          }
          
          return `assets/${extType}/[name]-[hash][extname]`
        },
        
        // JS文件命名
        chunkFileNames: 'assets/js/[name]-[hash].js',
        entryFileNames: 'assets/js/[name]-[hash].js'
      },
      
      // 外部化处理
      external: isProd ? [] : [],
      
      // 警告处理
      onwarn(warning, warn) {
        // 忽略某些不重要的警告
        if (warning.code === 'MODULE_LEVEL_DIRECTIVE') {
          return
        }
        
        if (warning.code === 'UNUSED_EXTERNAL_IMPORT') {
          return
        }
        
        warn(warning)
      }
    },
    chunkSizeWarningLimit: 1500, // 提高警告阈值
    reportCompressedSize: !isDev, // 只在生产环境报告压缩大小
    assetsInlineLimit: 8192, // 提高内联限制，减少HTTP请求
    
    // 预构建优化
    cssCodeSplit: true, // 启用CSS代码分割
    
    // 生产环境特定配置
    ...(isProd && {
      // 启用CSS最小化
      cssMinify: 'esbuild',
      
      // 启用更积极的Tree-shaking
      rollupOptions: {
        treeshake: {
          preset: 'recommended',
          manualPureFunctions: ['console.log', 'console.warn', 'console.info']
        }
      }
    })
  },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `
            @use "@/styles/variables.scss" as vars;
            @use "@/styles/design-tokens.scss" as tokens;
          `,
          api: 'modern-compiler',
          silenceDeprecations: ['legacy-js-api'] // 消除SCSS警告
        }
      },
      
      // CSS优化配置
      ...(isProd && {
        postcss: {
          plugins: [
            require('autoprefixer'),
            require('cssnano')({
              preset: ['default', {
                discardComments: {
                  removeAll: true
                },
                normalizeWhitespace: true
              }]
            })
          ]
        }
      }),
      
      // 开发环境CSS配置
      devSourcemap: isDev
    },
    
    optimizeDeps: {
      include: [
        'vue',
        'vue-router',
        'pinia',
        'element-plus/es',
        'element-plus/es/components/button/style/css',
        'element-plus/es/components/dialog/style/css',
        'element-plus/es/components/table/style/css',
        'element-plus/es/components/form/style/css',
        '@element-plus/icons-vue',
        'axios',
        'dayjs',
        'dayjs/plugin/relativeTime',
        'dayjs/plugin/utc',
        'echarts/core',
        'echarts/charts',
        'echarts/components',
        'echarts/renderers',
        'vue-echarts',
        'qrcode'
      ],
      exclude: [
        '@faker-js/faker', // 开发依赖
        'mockjs' // 模拟数据库
      ],
      
      // 预构建配置
      esbuildOptions: {
        target: 'esnext',
        define: {
          global: 'globalThis'
        }
      },
      
      entries: [
        'src/main.js',
        'src/router/index.js',
        'src/components/base/index.js'
      ],
      
      force: env.FORCE_REBUILD === 'true'
    },
    
    // 开发服务器优化
    esbuild: {
      target: 'esnext',
      legalComments: 'none', // 移除法律注释
      drop: isProd ? ['console', 'debugger'] : [],
      
      // 生产环境额外优化
      ...(isProd && {
        minifyIdentifiers: true,
        minifyWhitespace: true,
        minifySyntax: true,
        treeShaking: true
      })
    },
    
    // 实验性功能
    experimental: {
      renderBuiltUrl(filename, { hostType, type }) {
        // 为静态资源添加CDN前缀（如果配置了CDN）
        if (type === 'asset' && env.VITE_CDN_URL && isProd) {
          return `${env.VITE_CDN_URL}/${filename}`
        }
        return { relative: true }
      }
    },
    
    // 性能优化
    define: {
      __VUE_OPTIONS_API__: false, // 禁用Options API以减少包大小
      __VUE_PROD_DEVTOOLS__: false, // 生产环境禁用devtools
      __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: false
    }
  }
})
