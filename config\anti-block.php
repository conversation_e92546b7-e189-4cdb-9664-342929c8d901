<?php

return [
    /*
    |--------------------------------------------------------------------------
    | 防红系统配置
    |--------------------------------------------------------------------------
    |
    | 这里配置防红系统的基本参数和策略
    |
    */

    // 是否启用防红系统
    'enabled' => env('ANTI_BLOCK_ENABLED', true),

    // 开发模式配置
    'dev_mode' => env('ANTI_BLOCK_DEV_MODE', env('APP_ENV') === 'local'),

    // 默认跳转URL（当所有域名都不可用时）
    'default_url' => env('ANTI_BLOCK_DEFAULT_URL', 'https://www.baidu.com'),

    // 域名检测配置
    'domain_check' => [
        // 检测间隔（分钟）
        'interval' => env('ANTI_BLOCK_CHECK_INTERVAL', 10),
        
        // 检测超时时间（秒）
        'timeout' => env('ANTI_BLOCK_CHECK_TIMEOUT', 10),
        
        // 重试次数
        'retry_times' => env('ANTI_BLOCK_RETRY_TIMES', 3),
        
        // 检测方式：http, ping, dns
        'method' => env('ANTI_BLOCK_CHECK_METHOD', 'http'),
        
        // 健康分数阈值（低于此分数将被标记为异常）
        'health_threshold' => env('ANTI_BLOCK_HEALTH_THRESHOLD', 60),
    ],

    // 域名切换策略
    'switch_strategy' => [
        // 切换策略：immediate（立即切换）, delayed（延迟切换）, manual（手动切换）
        'type' => env('ANTI_BLOCK_SWITCH_STRATEGY', 'immediate'),
        
        // 延迟切换时间（分钟）
        'delay' => env('ANTI_BLOCK_SWITCH_DELAY', 5),
        
        // 切换冷却时间（分钟，防止频繁切换）
        'cooldown' => env('ANTI_BLOCK_SWITCH_COOLDOWN', 30),
    ],

    // 短链接配置
    'short_link' => [
        // 短码长度
        'code_length' => env('ANTI_BLOCK_SHORT_CODE_LENGTH', 6),
        
        // 短链接有效期（天）
        'expire_days' => env('ANTI_BLOCK_SHORT_EXPIRE_DAYS', 30),
        
        // 是否启用短链接统计
        'enable_stats' => env('ANTI_BLOCK_ENABLE_STATS', true),
    ],

    // 日志配置
    'logging' => [
        // 是否启用详细日志
        'detailed' => env('ANTI_BLOCK_DETAILED_LOG', true),
        
        // 日志保留天数
        'retention_days' => env('ANTI_BLOCK_LOG_RETENTION', 90),
        
        // 是否记录访问日志
        'access_log' => env('ANTI_BLOCK_ACCESS_LOG', true),
    ],

    // 微信检测配置
    'wechat_check' => [
        // 微信检测API（如果有的话）
        'api_url' => env('WECHAT_CHECK_API_URL', ''),
        
        // 检测User-Agent
        'user_agent' => 'Mozilla/5.0 (compatible; WeChatBot/1.0)',
        
        // 封禁关键词
        'blocked_keywords' => [
            'wx.qq.com/cgi-bin/mmwebwx-bin/blocked',
            '网页包含诱导分享、关注等诱导行为内容',
            '该链接无法访问',
        ],
    ],

    // QQ检测配置
    'qq_check' => [
        // QQ检测API（如果有的话）
        'api_url' => env('QQ_CHECK_API_URL', ''),
        
        // 检测User-Agent
        'user_agent' => 'Mozilla/5.0 (compatible; QQBot/1.0)',
        
        // 封禁关键词
        'blocked_keywords' => [
            'aq.qq.com',
            '该网页可能存在安全风险',
        ],
    ],

    // 域名池配置
    'domain_pool' => [
        // 默认优先级
        'default_priority' => 50,
        
        // 最小健康域名数量
        'min_healthy_domains' => 1,
        
        // 负载均衡策略：random（随机）, round_robin（轮询）, weighted（权重）
        'load_balance' => env('ANTI_BLOCK_LOAD_BALANCE', 'round_robin'),
    ],

    // 缓存配置
    'cache' => [
        // 缓存前缀
        'prefix' => 'anti_block:',
        
        // 域名状态缓存时间（分钟）
        'domain_status_ttl' => env('ANTI_BLOCK_CACHE_TTL', 30),
        
        // 健康域名缓存时间（分钟）
        'healthy_domains_ttl' => env('ANTI_BLOCK_HEALTHY_CACHE_TTL', 10),
    ],

    // 通知配置
    'notifications' => [
        // 是否启用通知
        'enabled' => env('ANTI_BLOCK_NOTIFICATIONS', true),
        
        // 通知方式：email, sms, webhook
        'channels' => ['email'],
        
        // 通知阈值（当可用域名数量低于此值时发送通知）
        'threshold' => env('ANTI_BLOCK_NOTIFICATION_THRESHOLD', 2),
        
        // 通知间隔（分钟，防止频繁通知）
        'interval' => env('ANTI_BLOCK_NOTIFICATION_INTERVAL', 60),
    ],

    // API配置
    'api' => [
        // API限流配置
        'rate_limit' => [
            'max_attempts' => 60,
            'decay_minutes' => 1,
        ],
        
        // API认证
        'auth_required' => true,
        
        // 允许的IP白名单
        'ip_whitelist' => env('ANTI_BLOCK_IP_WHITELIST', ''),
    ],

    // 统计配置
    'statistics' => [
        // 是否启用统计
        'enabled' => env('ANTI_BLOCK_STATS_ENABLED', true),
        
        // 统计数据保留天数
        'retention_days' => env('ANTI_BLOCK_STATS_RETENTION', 365),
        
        // 是否记录详细访问统计
        'detailed_access' => env('ANTI_BLOCK_DETAILED_ACCESS', false),
    ],

    // 安全配置
    'security' => [
        // 是否启用IP限制
        'ip_restriction' => env('ANTI_BLOCK_IP_RESTRICTION', false),
        
        // 单IP最大访问频率（每分钟）
        'max_requests_per_minute' => env('ANTI_BLOCK_MAX_REQUESTS', 100),
        
        // 是否启用User-Agent检查
        'user_agent_check' => env('ANTI_BLOCK_UA_CHECK', false),
        
        // 禁止的User-Agent模式
        'blocked_user_agents' => [
            'bot',
            'spider',
            'crawler',
        ],
    ],

    // 维护模式配置
    'maintenance' => [
        // 维护模式开关
        'enabled' => env('ANTI_BLOCK_MAINTENANCE', false),
        
        // 维护页面URL
        'page_url' => env('ANTI_BLOCK_MAINTENANCE_URL', '/maintenance'),
        
        // 维护模式消息
        'message' => '系统正在维护中，请稍后访问',
        
        // 允许访问的IP（维护期间）
        'allowed_ips' => env('ANTI_BLOCK_MAINTENANCE_IPS', ''),
    ],
];