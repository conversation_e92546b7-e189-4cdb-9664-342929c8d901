<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\GroupController;
use App\Http\Controllers\Api\OrderController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\AdminController;
use App\Http\Controllers\Api\DistributorControllerMerged as DistributorController;
use App\Http\Controllers\Api\UserManagementController;
use App\Http\Controllers\Api\TemplateController;
use App\Http\Controllers\Api\PartnerManagementController;
use App\Http\Controllers\Api\DistributionGroupController;
use App\Http\Controllers\Api\SettingsController;
use App\Http\Controllers\Api\DashboardController;
use App\Http\Controllers\Api\StatisticsController;
use App\Http\Controllers\Api\WithdrawalController;
use App\Http\Controllers\Api\CommissionController;
use App\Http\Controllers\Api\WechatGroupController;
use App\Http\Controllers\Api\SubstationController;
use App\Http\Controllers\Api\SubstationTemplateController;
use App\Http\Controllers\Api\SubstationAlertController;
use App\Http\Controllers\Api\SubstationAnalyticsController;
use App\Http\Controllers\Api\SubstationFinanceController;
use App\Http\Controllers\Api\AgentApplicationController;
use App\Http\Controllers\Api\AgentController;
use App\Http\Controllers\Api\FinanceController;
use App\Http\Controllers\Api\DistributionController;
use App\Http\Controllers\Api\CommissionLogController;
use App\Http\Controllers\Api\PaymentChannelController;
use App\Http\Controllers\Api\PaymentChannelManagementController;
use App\Http\Controllers\Api\PaymentConfigController;
use App\Http\Controllers\Api\PaymentPermissionController;
use App\Http\Controllers\Api\PaymentUsageLogController;
use App\Http\Controllers\Api\NotificationController;
use App\Http\Controllers\Api\SystemController;
use App\Http\Controllers\Api\PromotionLinkController;
use App\Http\Controllers\Api\AntiBlockController;
use App\Http\Controllers\Api\PaymentCallbackController;
use App\Http\Controllers\Api\LandingPageController;
use App\Http\Controllers\Api\TestController;
use App\Http\Controllers\Api\SystemMonitorController;
use App\Http\Controllers\Api\FreeAIController;
use App\Http\Controllers\Api\DevSetupController;
use App\Http\Controllers\Api\QuickTestController;
use App\Http\Controllers\Api\AvatarManagementController;
use App\Http\Controllers\Api\OperationLogController;
use App\Http\Controllers\Api\ExportController;
use App\Http\Controllers\Api\FileManagementController;
use App\Http\Controllers\Api\EnhancedWithdrawalController;
use App\Http\Controllers\Api\SimplePermissionController;
use App\Http\Controllers\Api\PermissionController;
use App\Http\Controllers\Api\EnhancedPermissionController;
use App\Services\AntiBlockService;

/*
|--------------------------------------------------------------------------
| API Routes  
|--------------------------------------------------------------------------
*/

// 开发环境快速设置路由（仅在开发环境可用）
if (app()->environment(['local', 'development'])) {
    Route::prefix('api/dev')->group(function () {
        Route::post('create-admin', [DevSetupController::class, 'createDevAdmin']);
        Route::post('create-test-data', [DevSetupController::class, 'createTestData']);
        Route::get('info', [DevSetupController::class, 'getDevInfo']);
    });
}

// 快速测试路由（无数据库依赖）- 始终可用于测试
Route::prefix('api/test')->group(function () {
    Route::post('login', [QuickTestController::class, 'testLogin']);
    Route::get('user', [QuickTestController::class, 'testUser']);
    Route::get('groups', [QuickTestController::class, 'testGroups']);
    Route::get('templates', [QuickTestController::class, 'testTemplates']);
});

// 系统设置API测试路由（无认证，直接访问）
Route::get('admin/system/settings', function() {
    return response()->json([
        'success' => true,
        'message' => '系统设置API工作正常',
        'timestamp' => now()->toDateTimeString(),
    ]);
});

// 代理商申请管理API测试路由（无认证，直接访问）
Route::prefix('admin/agent-applications')->group(function() {
    Route::get('/', function() {
        return response()->json([
            'success' => true,
            'code' => 200,
            'message' => '获取成功',
            'data' => [
                'data' => [
                    [
                        'id' => 1,
                        'user_id' => 101,
                        'username' => '测试代理商001',
                        'agent_type' => 'individual',
                        'agent_level' => 'bronze',
                        'status' => 'pending',
                        'application_reason' => '想要成为代理商推广产品',
                        'contact_info' => '13800138001',
                        'expected_commission_rate' => '10',
                        'created_at' => '2024-01-15 10:30:00',
                        'updated_at' => '2024-01-15 10:30:00'
                    ],
                    [
                        'id' => 2,
                        'user_id' => 102,
                        'username' => '测试代理商002',
                        'agent_type' => 'company',
                        'agent_level' => 'silver',
                        'status' => 'approved',
                        'application_reason' => '公司业务扩展需要',
                        'contact_info' => '13900139002',
                        'expected_commission_rate' => '15',
                        'created_at' => '2024-01-14 09:15:00',
                        'updated_at' => '2024-01-16 14:20:00'
                    ]
                ],
                'total' => 2,
                'per_page' => 20,
                'current_page' => 1,
                'last_page' => 1
            ]
        ]);
    });

    Route::get('stats', function() {
        return response()->json([
            'success' => true,
            'code' => 200,
            'message' => '获取成功',
            'data' => [
                'total_applications' => 15,
                'pending_count' => 5,
                'approved_count' => 8,
                'rejected_count' => 2,
                'today_applications' => 3,
                'week_applications' => 12,
                'month_applications' => 15
            ]
        ]);
    });
});

Route::prefix('admin/system')->group(function () {
    Route::get('settings', [App\Http\Controllers\Api\SystemTestController::class, 'getSettings']);
    Route::post('settings', [App\Http\Controllers\Api\SystemTestController::class, 'saveSettings']);
    Route::put('settings', [App\Http\Controllers\Api\SystemTestController::class, 'saveSettings']);
    Route::post('test-storage', [App\Http\Controllers\Api\SystemTestController::class, 'testStorage']);
});

// 支付管理API路由（无认证，直接访问）
Route::prefix('admin/payment-management')->group(function () {
    Route::get('methods', [App\Http\Controllers\Api\PaymentManagementController::class, 'getAvailablePaymentMethods']);
    Route::get('enabled-methods', [App\Http\Controllers\Api\PaymentManagementController::class, 'getEnabledPaymentMethods']);
    Route::post('toggle-method', [App\Http\Controllers\Api\PaymentManagementController::class, 'togglePaymentMethod']);
    Route::get('easypay/register-info', [App\Http\Controllers\Api\PaymentManagementController::class, 'getEasypayRegisterInfo']);
    Route::post('easypay/update-official-url', [App\Http\Controllers\Api\PaymentManagementController::class, 'updateEasypayOfficialUrl']);
    Route::post('test-config', [App\Http\Controllers\Api\PaymentManagementController::class, 'testPaymentConfig']);
});

// 管理员认证路由（不带前缀，直接匹配 /api/admin/auth/*）
Route::prefix('api/admin/auth')->group(function () {
    Route::post('login', [AuthController::class, 'adminLogin']);
    Route::middleware('auth:api')->group(function () {
        Route::get('user', [AuthController::class, 'me']);
        Route::post('logout', [AuthController::class, 'logout']);
        Route::post('refresh', [AuthController::class, 'refresh']);
    });
});

// 公开路由 - 添加速率限制
Route::prefix('v1')->middleware('throttle:60,1')->group(function () {
    // 健康检查
    Route::get('health', function () {
        return response()->json([
            'status' => 'ok',
            'timestamp' => now()->toISOString(),
            'version' => config('app.version', '1.0.0')
        ]);
    });
    
    // 认证相关路由
    Route::post('login', [AuthController::class, 'login']);
    Route::post('register', [AuthController::class, 'register']);
    Route::post('logout', [AuthController::class, 'logout']);
    
    // 微信群相关公开路由
    Route::get('groups', [WechatGroupController::class, 'index']);
    Route::get('groups/{id}', [WechatGroupController::class, 'show']);
    Route::post('groups/{id}/join', [WechatGroupController::class, 'join']);
    
    // 分销员登录
    Route::post('distributor/login', [DistributorController::class, 'login']);
    
    // 获取群组模板
    Route::get('templates', [TemplateController::class, 'index']);
    Route::get('templates/{id}', [TemplateController::class, 'show']);
    
    // 支付相关路由
    Route::prefix('payment')->group(function () {
        // 支付回调（使用专门的回调控制器）
        Route::post('notify/wechat', [PaymentCallbackController::class, 'wechatNotify']);
        Route::post('notify/alipay', [PaymentCallbackController::class, 'alipayNotify']);
        Route::post('notify/payoreo', [PaymentCallbackController::class, 'payoreoNotify']);
        Route::post('notify/qqpay', [PaymentCallbackController::class, 'qqpayNotify']);
        Route::post('notify/bank', [PaymentCallbackController::class, 'bankNotify']);
        Route::get('return', [PaymentCallbackController::class, 'paymentReturn']);
        
        // 兼容旧的通用回调接口
        Route::post('notify/{method}', [App\Http\Controllers\Api\PaymentController::class, 'notify']);
        
        // 其他支付相关接口
        Route::get('show/{orderNo}', [App\Http\Controllers\Api\PaymentController::class, 'show']);
        Route::get('methods', [App\Http\Controllers\Api\PaymentController::class, 'getPaymentMethods']);
        
        // 测试和日志接口（需要认证）
        Route::middleware('auth:api')->group(function () {
            Route::post('test-callback', [PaymentCallbackController::class, 'testCallback']);
            Route::get('callback-logs', [PaymentCallbackController::class, 'getCallbackLogs']);
        });
    });
    
    // 短链接跳转
    Route::get('s/{shortCode}', [App\Http\Controllers\ShortLinkController::class, 'redirect']);
    
    // 公开设置
    Route::get('public/settings', [SystemController::class, 'getPublicSettings']);
    Route::get('payment-channels/active', [PaymentChannelController::class, 'getActiveChannels']);
});

// 需要认证的路由
Route::prefix('v1')->middleware('auth:api')->group(function () {
    
    // 支付相关路由（需要认证）
    Route::prefix('payment')->group(function () {
        Route::post('create-order', [App\Http\Controllers\Api\PaymentController::class, 'createOrder']);
        Route::post('create-payment', [App\Http\Controllers\Api\PaymentController::class, 'createPayment']);
        Route::get('query/{orderNo}', [App\Http\Controllers\Api\PaymentController::class, 'queryOrder']);
        Route::post('cancel/{orderNo}', [App\Http\Controllers\Api\PaymentController::class, 'cancelOrder']);
    });
    
    // 用户相关路由
    Route::prefix('user')->group(function () {
        Route::get('profile', [UserController::class, 'profile']);
        Route::put('profile', [UserController::class, 'updateProfile']);
        Route::post('change-password', [UserController::class, 'changePassword']);
        Route::get('orders', [UserController::class, 'orders']);
        Route::get('groups', [UserController::class, 'groups']);
        Route::get('statistics', [UserController::class, 'statistics']);
        Route::post('avatar', [UserController::class, 'uploadAvatar']);
        Route::get('info', [AuthController::class, 'me']);
        Route::get('stats', [AuthController::class, 'getUserStats']);
    });
    
    // 认证相关
    Route::prefix('auth')->group(function () {
        Route::post('login', [AuthController::class, 'login']);
        Route::post('logout', [AuthController::class, 'logout']);
        Route::post('refresh', [AuthController::class, 'refresh']);
        Route::get('me', [AuthController::class, 'me']);
        Route::post('change-password', [AuthController::class, 'changePassword']);
        Route::post('update-profile', [AuthController::class, 'updateProfile']);
    });
    
    // 订单相关路由
    Route::prefix('orders')->group(function () {
        Route::get('/', [OrderController::class, 'index']);
        Route::post('/', [OrderController::class, 'store']);
        Route::get('{id}', [OrderController::class, 'show']);
        Route::put('{id}', [OrderController::class, 'update']);
        Route::delete('{id}', [OrderController::class, 'destroy']);
        Route::post('{id}/pay', [OrderController::class, 'pay']);
        Route::post('{id}/cancel', [OrderController::class, 'cancel']);
        Route::post('{id}/refund', [OrderController::class, 'refund']);
    });
    
    
    // 分销员相关路由
    Route::prefix('distributor')->group(function () {
        Route::get('dashboard', [DistributorController::class, 'dashboard']);
        Route::get('profile', [DistributorController::class, 'profile']);
        Route::put('profile', [DistributorController::class, 'updateProfile']);
        Route::get('groups', [DistributorController::class, 'groups']);
        Route::get('orders', [DistributorController::class, 'orders']);
        Route::get('statistics', [DistributorController::class, 'statistics']);
        Route::get('commissions', [DistributorController::class, 'commissions']);
        Route::get('withdrawals', [DistributorController::class, 'withdrawals']);
        
        // 分销员提现申请应用安全限制
        Route::middleware('withdrawal.permission')->group(function() {
            Route::post('withdrawals', [DistributorController::class, 'createWithdrawal']);
        });
        
        Route::get('sub-distributors', [DistributorController::class, 'subDistributors']);
        Route::post('sub-distributors', [DistributorController::class, 'createSubDistributor']);
        Route::get('templates', [DistributorController::class, 'templates']);
        Route::get('my-info', [DistributionController::class, 'getMyDistributionInfo']);
        Route::get('my-commissions', [DistributionController::class, 'getMyCommissions']);
        Route::post('generate-invite', [DistributionController::class, 'generateInviteLink']);
    });
    
    // 仪表板
    Route::prefix('dashboard')->group(function() {
        Route::get('stats', [DashboardController::class, 'getStats']);
        Route::get('chart-data', [DashboardController::class, 'getChartData']);
    });
    
    // 微信群管理
    Route::prefix('wechat-groups')->group(function () {
        Route::get('/', [WechatGroupController::class, 'index']);
        Route::post('/', [WechatGroupController::class, 'store']);
        Route::get('stats', [WechatGroupController::class, 'stats']);
        Route::get('{group}', [WechatGroupController::class, 'show']);
        Route::put('{group}', [WechatGroupController::class, 'update']);
        Route::delete('{group}', [WechatGroupController::class, 'destroy']);
        Route::get('{id}/detailed-stats', [WechatGroupController::class, 'getDetailedStats']);
        Route::patch('{id}/status', [WechatGroupController::class, 'updateStatus']);
        Route::post('{id}/qr-code', [WechatGroupController::class, 'updateQrCode']);
        Route::post('batch-delete', [WechatGroupController::class, 'batchDelete']);
        Route::get('export', [WechatGroupController::class, 'export']);
        Route::post('upload-cover', [WechatGroupController::class, 'uploadCover']);
        
        // 内容管理相关路由
        Route::get('{id}/content', [WechatGroupController::class, 'getGroupContent']);
        Route::put('{id}/content', [WechatGroupController::class, 'updateGroupContent']);
        Route::get('{id}/members', [WechatGroupController::class, 'getGroupMembers']);
        Route::get('{id}/analytics', [WechatGroupController::class, 'getGroupAnalytics']);
        
        // 营销功能路由
        Route::get('{id}/marketing-config', [WechatGroupController::class, 'getMarketingConfig']);
        Route::put('{id}/marketing-config', [WechatGroupController::class, 'updateMarketingConfig']);
        Route::post('{id}/virtual-members', [WechatGroupController::class, 'generateVirtualMembers']);
    });
    
    // 推广链接
    Route::apiResource('promotion-links', PromotionLinkController::class);
    
    // 防红系统管理
    Route::prefix('anti-block')->name('anti-block.')->group(function () {
        Route::get('overview', [AntiBlockController::class, 'overview'])->name('overview');
        Route::post('batch-check-domains', [AntiBlockController::class, 'batchCheckDomains'])->name('batch-check-domains');
        Route::get('system-config', [AntiBlockController::class, 'getSystemConfig'])->name('system-config');
        Route::put('system-config', [AntiBlockController::class, 'updateSystemConfig'])->name('update-system-config');
        Route::get('logs', [AntiBlockController::class, 'getLogs'])->name('logs');
        
        // 群组防红管理
        Route::prefix('groups/{groupId}')->name('groups.')->group(function () {
            Route::get('config', [AntiBlockController::class, 'getGroupAntiBlockConfig'])->name('config');
            Route::put('config', [AntiBlockController::class, 'updateGroupAntiBlockConfig'])->name('update-config');
            Route::post('generate-link', [AntiBlockController::class, 'generateGroupPromotionLink'])->name('generate-link');
            // 注释掉重复的路由，避免冲突
            // Route::post('promotion-link', [AntiBlockController::class, 'generateGroupPromotionLink'])->name('promotion-link');
            Route::post('check-switch-domain', [AntiBlockController::class, 'checkAndSwitchGroupDomain'])->name('check-switch-domain');
            Route::get('promotion-links', [AntiBlockController::class, 'getGroupPromotionLinks'])->name('promotion-links');
        });
        
        // 推广链接批量操作
        Route::post('promotion-links/batch-operate', [AntiBlockController::class, 'batchOperatePromotionLinks'])->name('promotion-links.batch-operate');
        
        // 域名健康报告
        Route::get('domain-pools/{domainPoolId}/health-report', [AntiBlockController::class, 'getDomainHealthReport'])->name('domain-health-report');
    });
    
    // 短链接相关路由
    Route::prefix('short-links')->group(function () {
        Route::get('{shortCode}/info', [App\Http\Controllers\ShortLinkController::class, 'info']);
        Route::get('{shortCode}/preview', [App\Http\Controllers\ShortLinkController::class, 'preview']);
        Route::get('{shortCode}/stats', [App\Http\Controllers\ShortLinkController::class, 'stats']);
        Route::get('{shortCode}/qrcode', [App\Http\Controllers\ShortLinkController::class, 'qrcode']);
        Route::post('batch-check', [App\Http\Controllers\ShortLinkController::class, 'batchCheck']);
        Route::get('popular', [App\Http\Controllers\ShortLinkController::class, 'popular']);
        Route::get('health-check', [App\Http\Controllers\ShortLinkController::class, 'healthCheck']);
    });
    
    // 财务相关
    Route::prefix('finance')->group(function() {
        Route::get('overview', [FinanceController::class, 'getOverview']);
        Route::get('earnings-chart', [FinanceController::class, 'getEarningsChart']);
        Route::get('transactions', [FinanceController::class, 'getUserTransactions']);
        Route::get('withdrawals', [FinanceController::class, 'getUserWithdrawals']);
        Route::get('commissions', [FinanceController::class, 'getUserCommissions']);
        
        // 用户提现申请应用安全限制
        Route::middleware('withdrawal.permission')->group(function() {
            Route::post('withdrawals', [FinanceController::class, 'requestWithdrawal']);
            Route::post('withdrawals/enhanced', [EnhancedWithdrawalController::class, 'submitWithdrawal']);
        });
        
        Route::post('recharge', [FinanceController::class, 'createRechargeOrder']);
    });
    
    // 通知
    Route::prefix('notifications')->group(function () {
        Route::get('/', [NotificationController::class, 'index']);
        Route::post('/mark-as-read', [NotificationController::class, 'markAsRead']);
    });
    
    // 代理商申请（用户端）
    Route::prefix('agent-applications')->group(function () {
        Route::post('/', [AgentApplicationController::class, 'store']);
        Route::get('my', [AgentApplicationController::class, 'getMy']);
        Route::post('{id}/cancel', [AgentApplicationController::class, 'cancel']);
        Route::get('available-substations', [AgentApplicationController::class, 'getAvailableSubstations']);
    });
    
    // 简化权限管理
    Route::prefix('permissions')->group(function () {
        // 用户权限管理
        Route::get('users', [SimplePermissionController::class, 'getUserList']);
        Route::get('users/{userId}/permissions', [SimplePermissionController::class, 'getUserPermissions']);
        Route::put('users/{userId}/permissions', [SimplePermissionController::class, 'updateUserPermissions']);
        Route::get('users/search', [SimplePermissionController::class, 'searchUsers']);
        
        // 角色管理
        Route::get('roles', [SimplePermissionController::class, 'getRoleList']);
        Route::post('roles', [SimplePermissionController::class, 'createRole']);
        Route::put('roles/{roleId}', [SimplePermissionController::class, 'updateRole']);
        Route::delete('roles/{roleId}', [SimplePermissionController::class, 'deleteRole']);
        
        // 权限列表和统计
        Route::get('permissions', [SimplePermissionController::class, 'getPermissionList']);
        Route::get('matrix', [SimplePermissionController::class, 'getPermissionMatrix']);
        Route::get('stats', [SimplePermissionController::class, 'getPermissionStats']);
    });

    // 免费AI内容生成
    Route::prefix('ai')->group(function () {
        Route::post('generate-content', [FreeAIController::class, 'generateContent']);
        Route::post('batch-generate', [FreeAIController::class, 'batchGenerate']);
        Route::get('provider-status', [FreeAIController::class, 'getProviderStatus']);
        Route::get('content-types', [FreeAIController::class, 'getContentTypes']);
        Route::post('optimize-content', [FreeAIController::class, 'optimizeContent']);
        Route::post('rate-content', [FreeAIController::class, 'rateContent']);
        Route::post('generate-multilanguage', [FreeAIController::class, 'generateMultiLanguage']);
        Route::get('generation-history', [FreeAIController::class, 'getGenerationHistory']);
    });
    
    // 内容质量分析
    Route::prefix('content-quality')->group(function () {
        Route::post('analyze', [App\Http\Controllers\Api\ContentQualityController::class, 'analyzeContent']);
        Route::post('optimize', [App\Http\Controllers\Api\ContentQualityController::class, 'optimizeContent']);
        Route::post('ab-test-variants', [App\Http\Controllers\Api\ContentQualityController::class, 'generateABTestVariants']);
        Route::get('system-status', [App\Http\Controllers\Api\ContentQualityController::class, 'getSystemStatus']);
    });
    
    // 文件上传
    Route::prefix('upload')->group(function () {
        Route::post('image', [UserController::class, 'uploadImage']);
        Route::post('avatar', [UserController::class, 'uploadAvatar']);
        Route::post('template', [TemplateController::class, 'uploadImage']);
    });
    
    // 管理员相关路由
    Route::prefix('api/admin')->middleware('role:admin')->group(function () {
        // 仪表板
        Route::get('dashboard', [AdminController::class, 'dashboard']);
        Route::get('dashboard/full-stats', [DashboardController::class, 'getFullStatsForAdmin']);
        Route::get('dashboard/charts', [DashboardController::class, 'getDashboardCharts']);
        Route::get('dashboard/income-chart', [DashboardController::class, 'getIncomeChart']);
        Route::get('dashboard/user-growth-chart', [DashboardController::class, 'getUserGrowthChart']);
        Route::get('dashboard/order-chart', [DashboardController::class, 'getOrderChart']);
        Route::get('dashboard/region-chart', [DashboardController::class, 'getRegionChart']);
        Route::get('dashboard/popular-groups', [DashboardController::class, 'getPopularGroups']);
        Route::get('dashboard/active-users', [DashboardController::class, 'getActiveUsers']);
        Route::get('dashboard/recent-activities', [DashboardController::class, 'getRecentActivities']);
        
        // 前端调用的缺失路由
        Route::get('dashboard/income-trend', [DashboardController::class, 'getIncomeTrend']);
        Route::get('dashboard/order-source', [DashboardController::class, 'getOrderSourceDistribution']);
        Route::get('dashboard/user-growth', [DashboardController::class, 'getUserGrowthData']);
        Route::get('dashboard/system-status', [DashboardController::class, 'getSystemStatus']);
        Route::get('dashboard/recent-orders', [DashboardController::class, 'getRecentOrders']);
        Route::get('dashboard/top-distributors', [DashboardController::class, 'getTopDistributors']);
        Route::get('dashboard/system-overview', [DashboardController::class, 'getSystemOverview']);
        Route::get('dashboard/business-metrics', [DashboardController::class, 'getBusinessMetrics']);
        Route::get('dashboard/realtime', [DashboardController::class, 'getRealTimeData']);
        Route::get('dashboard/notifications', [DashboardController::class, 'getNotifications']);
        Route::post('dashboard/notifications/{id}/read', [DashboardController::class, 'markNotificationRead']);
        Route::get('dashboard/todos', [DashboardController::class, 'getTodoList']);
        
        // 用户管理
        Route::prefix('users')->group(function () {
            Route::get('/', [UserManagementController::class, 'getUserList']);
            Route::get('stats', [UserManagementController::class, 'getUserStats']); // 添加统计端点
            Route::post('/', [UserManagementController::class, 'createUser']);
            Route::get('{id}', [UserManagementController::class, 'getUserDetail']);
            Route::put('{id}', [UserManagementController::class, 'updateUser']);
            Route::delete('{id}', [UserManagementController::class, 'deleteUser']);
            Route::put('{id}/reset-password', [UserManagementController::class, 'resetPassword']);
            Route::post('batch', [UserManagementController::class, 'batchCreateUsers']);
            Route::put('batch-status', [UserManagementController::class, 'batchUpdateStatus']); // 添加批量状态更新
            Route::get('export', [UserManagementController::class, 'exportUsers']);
            Route::get('template', [UserManagementController::class, 'downloadTemplate']);
            Route::post('{user}/status', [UserController::class, 'updateStatus']);
            Route::post('{user}/adjust-balance', [UserController::class, 'adjustBalance']);
            Route::get('{user}/balance-logs', [UserController::class, 'getBalanceLogs']);
            Route::get('{user}/children', [UserController::class, 'getChildren']);
            Route::get('{user}/stats', [UserController::class, 'getUserStats']);
            Route::post('{user}/reset-password', [UserController::class, 'resetPassword']);
        });
        
        // 群组管理
        Route::prefix('groups')->group(function () {
            Route::get('/', [WechatGroupController::class, 'adminIndex']);
            Route::get('stats', [WechatGroupController::class, 'getGroupStats']); // 添加统计端点
            Route::get('{id}', [WechatGroupController::class, 'adminShow']);
            Route::put('{id}', [WechatGroupController::class, 'adminUpdate']);
            Route::delete('{id}', [WechatGroupController::class, 'adminDestroy']);
            Route::post('batch', [WechatGroupController::class, 'batchOperation']);
            Route::put('batch-status', [WechatGroupController::class, 'batchUpdateStatus']); // 添加批量状态更新
            Route::get('{id}/statistics', [WechatGroupController::class, 'statistics']);
        });
        
        // 头像管理
        Route::prefix('avatars')->group(function () {
            // 分类管理
            Route::get('categories', [AvatarManagementController::class, 'getCategories']);
            
            // 头像管理
            Route::get('category/{categoryId}', [AvatarManagementController::class, 'getAvatarsByCategory']);
            Route::post('upload', [AvatarManagementController::class, 'uploadAvatar']);
            Route::put('{avatarId}', [AvatarManagementController::class, 'updateAvatar']);
            Route::delete('{avatarId}', [AvatarManagementController::class, 'deleteAvatar']);
            Route::post('batch-delete', [AvatarManagementController::class, 'batchDeleteAvatars']);
            
            // 使用统计
            Route::post('record-usage', [AvatarManagementController::class, 'recordUsage']);
            Route::get('usage-stats', [AvatarManagementController::class, 'getUsageStats']);
        });
        
        // 订单管理
        Route::prefix('orders')->group(function () {
            Route::get('/', [OrderController::class, 'adminIndex']);
            Route::get('full-list', [OrderController::class, 'getFullOrderList']);
            Route::get('stats', [OrderController::class, 'getOrderStats']);
            Route::post('batch-process', [OrderController::class, 'batchProcess']);
            Route::get('export', [OrderController::class, 'exportOrders']);
            Route::get('{id}', [OrderController::class, 'adminShow']);
            Route::put('{id}', [OrderController::class, 'adminUpdate']);
            Route::delete('{id}', [OrderController::class, 'adminDestroy']);
            Route::post('{id}/refund', [OrderController::class, 'refund']);
            Route::post('batch', [OrderController::class, 'batchOperation']);
            Route::get('statistics', [OrderController::class, 'statistics']);
        });
        
        // 模板管理
        Route::prefix('templates')->group(function () {
            Route::get('/', [TemplateController::class, 'adminIndex']);
            Route::post('/', [TemplateController::class, 'store']);
            Route::get('{id}', [TemplateController::class, 'adminShow']);
            Route::put('{id}', [TemplateController::class, 'update']);
            Route::delete('{id}', [TemplateController::class, 'destroy']);
            Route::post('batch', [TemplateController::class, 'batchOperation']);
            Route::post('upload', [TemplateController::class, 'uploadImage']);
        });
        
        // 分销组管理
        Route::prefix('distribution-groups')->group(function () {
            Route::get('/', [DistributionGroupController::class, 'index']);
            Route::post('/', [DistributionGroupController::class, 'store']);
            Route::get('{id}', [DistributionGroupController::class, 'show']);
            Route::put('{id}', [DistributionGroupController::class, 'update']);
            Route::delete('{id}', [DistributionGroupController::class, 'destroy']);
            Route::post('batch-delete', [DistributionGroupController::class, 'batchDelete']);
            Route::post('{id}/members', [DistributionGroupController::class, 'addMember']);
            Route::delete('{id}/members', [DistributionGroupController::class, 'removeMember']);
            Route::get('{id}/stats', [DistributionGroupController::class, 'getStats']);
            Route::post('batch-status', [DistributionGroupController::class, 'batchUpdateStatus']);
        });
        
        // 分销员管理
        Route::prefix('distributors')->group(function () {
            Route::get('/', [DistributorController::class, 'adminIndex']);
            Route::get('stats', [DistributorController::class, 'getStats']);
            Route::get('{id}', [DistributorController::class, 'adminShow']);
            Route::put('{id}', [DistributorController::class, 'adminUpdate']);
            Route::delete('{id}', [DistributorController::class, 'adminDestroy']);
            Route::put('{id}/status', [DistributorController::class, 'updateStatus']);
            Route::put('{id}/upgrade', [DistributorController::class, 'upgrade']);
            Route::put('{id}/balance', [DistributorController::class, 'manageBalance']);
            Route::put('{id}/level', [DistributorController::class, 'updateLevel']);
            Route::put('{id}/group', [DistributorController::class, 'updateDistributionGroup']);
            Route::post('{id}/templates', [DistributorController::class, 'assignTemplates']);
            Route::get('{id}/statistics', [DistributorController::class, 'adminStatistics']);
        });
        
        // 提现管理
        Route::prefix('withdrawals')->middleware('withdrawal.permission')->group(function () {
            Route::get('/', [WithdrawalController::class, 'adminIndex']);
            Route::get('{id}', [WithdrawalController::class, 'adminShow']);
            Route::put('{id}/approve', [WithdrawalController::class, 'approve']);
            Route::put('{id}/reject', [WithdrawalController::class, 'reject']);
            Route::put('{id}/complete', [WithdrawalController::class, 'complete']);
            Route::post('{id}/mark-transferred', [WithdrawalController::class, 'markTransferred']);
            Route::post('{id}/mark-failed', [WithdrawalController::class, 'markFailed']);
            Route::post('batch-approve', [WithdrawalController::class, 'batchApprove']);
            Route::post('batch-reject', [WithdrawalController::class, 'batchReject']);
            Route::get('statistics', [WithdrawalController::class, 'statistics']);
        });
        
        // 佣金管理
        Route::prefix('commissions')->group(function () {
            Route::get('/', [CommissionController::class, 'adminIndex']);
            Route::get('{id}', [CommissionController::class, 'adminShow']);
            Route::post('recalculate', [CommissionController::class, 'recalculate']);
            Route::get('statistics', [CommissionController::class, 'statistics']);
        });
        
        // 佣金记录管理
        Route::prefix('commission-logs')->group(function () {
            Route::get('/', [CommissionLogController::class, 'index']);
            Route::get('{id}', [CommissionLogController::class, 'show']);
            Route::put('{id}', [CommissionLogController::class, 'update']);
            Route::delete('{id}', [CommissionLogController::class, 'destroy']);
            Route::post('batch-settle', [CommissionLogController::class, 'batchSettle']);
            Route::post('{log}/settle', [CommissionLogController::class, 'settle']);
        });
        
        // 财务管理
        Route::prefix('finance')->group(function() {
            Route::get('dashboard-stats', [FinanceController::class, 'getDashboardStats']);
            Route::get('trend-data', [FinanceController::class, 'getTrendData']);
            Route::get('income-sources', [FinanceController::class, 'getIncomeSources']);
            Route::get('transactions', [FinanceController::class, 'getTransactionList']);
            Route::get('withdrawals', [FinanceController::class, 'getWithdrawRecords']);
            Route::get('withdrawals/stats', [FinanceController::class, 'getWithdrawStats']);
            
            // 提现审核相关操作应用安全中间件
            Route::middleware('withdrawal.permission')->group(function() {
                Route::post('withdrawals/{id}/approve', [FinanceController::class, 'approveWithdraw']);
                Route::post('withdrawals/{id}/reject', [FinanceController::class, 'rejectWithdraw']);
                Route::post('withdrawals/{id}/process', [FinanceController::class, 'processWithdraw']);
                Route::post('withdrawals/batch-approve', [FinanceController::class, 'batchApproveWithdraw']);
                Route::post('withdrawals/batch-reject', [FinanceController::class, 'batchRejectWithdraw']);
                
                // 增强提现功能
                Route::post('withdrawals/quick-approval', [EnhancedWithdrawalController::class, 'quickApproval']);
                Route::post('withdrawals/smart-approval', [EnhancedWithdrawalController::class, 'smartApproval']);
            });
            
            Route::get('withdrawals/enhanced-stats', [EnhancedWithdrawalController::class, 'getWithdrawalStats']);
            Route::get('recent-transactions', [FinanceController::class, 'getTransactionList']);
            Route::get('export-report', [FinanceController::class, 'exportReport']);
        });
        
        // 渠道伙伴管理（统一代理商和分销商）
        Route::prefix('partner-management')->group(function() {
            Route::get('overview', [PartnerManagementController::class, 'getOverview']);
            Route::get('partners', [PartnerManagementController::class, 'getPartners']);
            Route::post('partners', [PartnerManagementController::class, 'createPartner']);
            Route::get('partners/{id}', [PartnerManagementController::class, 'getPartnerDetail']);
            Route::put('partners/{id}', [PartnerManagementController::class, 'updatePartner']);
            Route::post('partners/{id}/reset-password', [PartnerManagementController::class, 'resetPassword']);
            Route::post('batch', [PartnerManagementController::class, 'batchOperation']);
            Route::get('distribution-groups', [PartnerManagementController::class, 'getDistributionGroups']);
            Route::get('hierarchy', [PartnerManagementController::class, 'getHierarchy']);
            Route::get('commissions', [PartnerManagementController::class, 'getCommissions']);
            Route::get('export', [PartnerManagementController::class, 'exportData']);
        });
        
        // 兼容性路由 - 分销管理（将逐步废弃）
        Route::prefix('distribution')->group(function() {
            Route::get('overview', [DistributionController::class, 'getOverview']);
            Route::get('distributors', [DistributionController::class, 'getDistributors']);
            Route::get('groups', [DistributionController::class, 'getGroups']);
            Route::get('commissions', [DistributionController::class, 'getCommissions']);
            Route::get('level-config', [DistributionController::class, 'getLevelConfig']);
            Route::post('batch-operation', [DistributionController::class, 'batchOperation']);
            Route::get('export-data', [DistributionController::class, 'exportData']);
        });
        
        // 推广管理
        Route::prefix('promotions')->group(function () {
            Route::get('/', [App\Http\Controllers\Api\Admin\PromotionController::class, 'index']);
            Route::post('/', [App\Http\Controllers\Api\Admin\PromotionController::class, 'store']);
            Route::get('stats', [App\Http\Controllers\Api\Admin\PromotionController::class, 'getStats']);
            Route::get('{promotionLink}', [App\Http\Controllers\Api\Admin\PromotionController::class, 'show']);
            Route::put('{promotionLink}', [App\Http\Controllers\Api\Admin\PromotionController::class, 'update']);
            Route::delete('{promotionLink}', [App\Http\Controllers\Api\Admin\PromotionController::class, 'destroy']);
            Route::post('batch-action', [App\Http\Controllers\Api\Admin\PromotionController::class, 'batchAction']);
            Route::post('generate-materials', [App\Http\Controllers\Api\Admin\PromotionController::class, 'generateMaterials']);
            Route::post('{promotionLink}/duplicate', [App\Http\Controllers\Api\Admin\PromotionController::class, 'duplicate']);
            Route::post('{promotionLink}/regenerate-code', [App\Http\Controllers\Api\Admin\PromotionController::class, 'regenerateShortCode']);
            Route::get('expiring-links', [App\Http\Controllers\Api\Admin\PromotionController::class, 'getExpiringLinks']);
            Route::post('cleanup-expired', [App\Http\Controllers\Api\Admin\PromotionController::class, 'cleanupExpired']);
        });

        // 反屏蔽系统
        Route::prefix('anti-block')->group(function () {
            Route::get('stats', [AntiBlockController::class, 'getStats']);
            Route::get('domains', [AntiBlockController::class, 'index']);
            Route::post('domains', [AntiBlockController::class, 'store']);
            Route::get('domains/{id}', [AntiBlockController::class, 'show']);
            Route::put('domains/{id}', [AntiBlockController::class, 'update']);
            Route::delete('domains/{id}', [AntiBlockController::class, 'destroy']);
            Route::post('domains/batch-delete', [AntiBlockController::class, 'batchDeleteDomains']);
            Route::post('domains/batch-check', [AntiBlockController::class, 'batchCheckDomains']);
            Route::get('short-links', [AntiBlockController::class, 'getShortLinks']);
            Route::post('short-links', [AntiBlockController::class, 'createShortLink']);
            Route::delete('short-links/{id}', [AntiBlockController::class, 'deleteShortLink']);
            Route::get('access-stats', [AntiBlockController::class, 'getAccessStats']);
            Route::get('access-logs', [AntiBlockController::class, 'getAccessLogs']);
            Route::get('click-trends', [AntiBlockController::class, 'getClickTrends']);
            Route::get('region-stats', [AntiBlockController::class, 'getRegionStats']);
            Route::get('platform-stats', [AntiBlockController::class, 'getPlatformStats']);
            Route::post('qrcode', [AntiBlockController::class, 'generateQRCode']);
            
            // 防红分析专用API路由
            Route::get('analytics', [AntiBlockController::class, 'getAnalyticsData']);
            Route::get('real-time-monitor', [AntiBlockController::class, 'getRealTimeMonitor']);
            Route::get('domain-status', [AntiBlockController::class, 'getDomainStatus']);
            Route::get('recent-access-logs', [AntiBlockController::class, 'getRecentAccessLogs']);
            Route::post('domains/{id}/check', [AntiBlockController::class, 'checkDomain']);
            Route::post('domains/check-all', [AntiBlockController::class, 'checkAllDomains']);
            Route::get('analytics/export', [AntiBlockController::class, 'exportAnalyticsReport']);
        });
        
        // 落地页管理
        Route::prefix('landing-pages')->group(function () {
            Route::get('/', [LandingPageController::class, 'index']);
            Route::post('/', [LandingPageController::class, 'store']);
            Route::get('/types', [LandingPageController::class, 'getPageTypes']);
            Route::get('/default-config', [LandingPageController::class, 'getDefaultConfig']);
            Route::get('/{landingPage}', [LandingPageController::class, 'show']);
            Route::put('/{landingPage}', [LandingPageController::class, 'update']);
            Route::delete('/{landingPage}', [LandingPageController::class, 'destroy']);
            Route::post('/{landingPage}/preview', [LandingPageController::class, 'preview']);
        });
        
        // 支付渠道管理
        Route::prefix('payment-channels')->group(function () {
            Route::get('/', [PaymentChannelController::class, 'index']);
            Route::post('/', [PaymentChannelController::class, 'store']);
            Route::get('{id}', [PaymentChannelController::class, 'show']);
            Route::put('{id}', [PaymentChannelController::class, 'update']);
            Route::delete('{id}', [PaymentChannelController::class, 'destroy']);
        });
        
        // 支付通道管理（系统管理员专用）
        Route::prefix('payment-channel-management')->group(function () {
            Route::get('/', [PaymentChannelManagementController::class, 'index']);
            Route::post('toggle-status', [PaymentChannelManagementController::class, 'toggleStatus']);
            Route::post('grant-permissions', [PaymentChannelManagementController::class, 'grantPermissions']);
            Route::get('statistics', [PaymentChannelManagementController::class, 'statistics']);
            Route::get('overview', [PaymentChannelManagementController::class, 'overview']);
            Route::get('{channelCode}/users', [PaymentChannelManagementController::class, 'getChannelUsers']);
            Route::put('channels/{id}', [PaymentChannelManagementController::class, 'updateChannel']);
        });
        
        // 支付配置管理
        Route::prefix('payment-configs')->group(function () {
            Route::get('/', [PaymentConfigController::class, 'index']);
            Route::post('/', [PaymentConfigController::class, 'store']);
            Route::get('{id}', [PaymentConfigController::class, 'show']);
            Route::put('{id}', [PaymentConfigController::class, 'update']);
            Route::delete('{id}', [PaymentConfigController::class, 'destroy']);
            Route::post('test', [PaymentConfigController::class, 'test']);
            Route::get('template/{channelCode}', [PaymentConfigController::class, 'template']);
            Route::get('available-channels', [PaymentConfigController::class, 'availableChannels']);
        });
        
        // 支付权限管理
        Route::prefix('payment-permissions')->group(function () {
            Route::get('/', [PaymentPermissionController::class, 'index']);
            Route::post('grant', [PaymentPermissionController::class, 'grant']);
            Route::post('revoke', [PaymentPermissionController::class, 'revoke']);
            Route::post('check', [PaymentPermissionController::class, 'check']);
            Route::get('expiring', [PaymentPermissionController::class, 'expiring']);
            Route::get('statistics', [PaymentPermissionController::class, 'statistics']);
        });
        
        // 支付使用日志
        Route::prefix('payment-logs')->group(function () {
            Route::get('/', [PaymentUsageLogController::class, 'index']);
            Route::get('{id}', [PaymentUsageLogController::class, 'show']);
            Route::get('statistics/payment', [PaymentUsageLogController::class, 'statistics']);
            Route::get('statistics/channel', [PaymentUsageLogController::class, 'channelStatistics']);
            Route::post('export', [PaymentUsageLogController::class, 'export']);
        });
        
        // 注意：dashboard路由已在上面定义，此处删除重复定义
        
        // 导出管理
        Route::prefix('exports')->group(function () {
            Route::get('/', [ExportController::class, 'adminIndex']);
            Route::delete('{id}', [ExportController::class, 'adminDestroy']);
            Route::post('cleanup', [ExportController::class, 'cleanup']);
        });
        
        // 系统设置
        Route::prefix('system')->group(function () {
            Route::get('settings', [SystemController::class, 'getSettings']);
            Route::post('settings', [SystemController::class, 'saveSettings']);
            Route::put('settings', [SystemController::class, 'saveSettings']);
            Route::post('test-storage', [SystemController::class, 'testStorage']);
            Route::get('logs', [SystemController::class, 'getSystemLogs']);
            Route::get('info', [SystemController::class, 'getSystemInfo']);
            Route::get('statistics', [SystemController::class, 'getSystemStats']);
            Route::get('realtime', [SystemController::class, 'getRealtimeData']);
            Route::post('clear-cache', [SystemController::class, 'clearCache']);
            Route::post('restart-queue', [SystemController::class, 'restartQueue']);
            Route::post('logs/clean', [SystemController::class, 'cleanLogs']);
            Route::get('services', [SystemController::class, 'getServiceStatus']);
            Route::post('services/{name}/{action}', [SystemController::class, 'controlService']);
            Route::get('logs/export', [SystemController::class, 'exportLogs']);
            Route::get('health', [SystemController::class, 'getHealth']);
        });
        
        // 增强权限管理系统
        Route::prefix('permissions')->middleware('permission:permission.manage')->group(function () {
            // 权限管理仪表板
            Route::get('dashboard', [EnhancedPermissionController::class, 'getDashboardData']);
            
            // 角色管理
            Route::prefix('roles')->group(function () {
                Route::get('/', [PermissionController::class, 'getRoles']);
                Route::post('/', [PermissionController::class, 'createRole']);
                Route::put('{id}', [PermissionController::class, 'updateRole']);
                Route::delete('{id}', [PermissionController::class, 'deleteRole']);
                Route::get('{id}/permissions', [PermissionController::class, 'getRolePermissions']);
                Route::put('{id}/permissions', [PermissionController::class, 'assignPermissions']);
                Route::get('{id}/users', [PermissionController::class, 'getRoleUsers']);
                Route::post('{id}/users', [PermissionController::class, 'assignUsersToRole']);
            });
            
            // 权限管理
            Route::prefix('permissions')->group(function () {
                Route::get('/', [PermissionController::class, 'getPermissions']);
                Route::post('/', [PermissionController::class, 'createPermission']);
                Route::get('stats', [PermissionController::class, 'getPermissionStats']);
                Route::post('initialize', [PermissionController::class, 'initializePermissions']);
            });
            
            // 用户权限管理
            Route::prefix('users')->group(function () {
                Route::get('{userId}/permissions', [PermissionController::class, 'getUserPermissions']);
                Route::post('{userId}/permissions', [PermissionController::class, 'assignUserPermissions']);
                Route::post('{userId}/roles', [PermissionController::class, 'assignUserRole']);
                Route::get('{userId}/history', [EnhancedPermissionController::class, 'getPermissionHistory']);
            });
            
            // 批量权限操作
            Route::post('batch-update', [EnhancedPermissionController::class, 'batchUpdateUserPermissions']);
            Route::post('batch-analyze', [EnhancedPermissionController::class, 'analyzePermissionImpact']);
            
            // 权限审计和验证
            Route::prefix('audit')->group(function () {
                Route::get('history', [EnhancedPermissionController::class, 'getPermissionHistory']);
                Route::post('validate-config', [EnhancedPermissionController::class, 'validatePermissionConfig']);
                Route::get('trails', [EnhancedPermissionController::class, 'getAuditTrails']);
            });
            
            // 权限缓存管理
            Route::prefix('cache')->group(function () {
                Route::delete('clear', [EnhancedPermissionController::class, 'clearPermissionCache']);
                Route::get('status', [EnhancedPermissionController::class, 'getCacheStatus']);
            });
            
            // 权限配置导入导出
            Route::post('export', [EnhancedPermissionController::class, 'exportPermissions']);
            Route::post('import', [EnhancedPermissionController::class, 'importPermissions']);
            Route::post('backup', [SystemController::class, 'backupDatabase']);
            Route::post('restore', [SystemController::class, 'restoreDatabase']);
        });
        
        // 操作日志管理
        Route::prefix('operation-logs')->group(function () {
            Route::get('/', [OperationLogController::class, 'index']);
            Route::get('statistics', [OperationLogController::class, 'getStatistics']);
            Route::post('/', [OperationLogController::class, 'store']);
            Route::delete('batch', [OperationLogController::class, 'batchDelete']);
            Route::post('cleanup', [OperationLogController::class, 'cleanup']);
            Route::post('export', [OperationLogController::class, 'export']);
        });
        
        // 文件管理
        Route::prefix('files')->group(function () {
            Route::get('/', [FileManagementController::class, 'index']);
            Route::post('upload', [FileManagementController::class, 'upload']);
            Route::delete('/', [FileManagementController::class, 'delete']);
            Route::post('create-directory', [FileManagementController::class, 'createDirectory']);
            Route::post('rename', [FileManagementController::class, 'rename']);
            Route::get('storage-stats', [FileManagementController::class, 'getStorageStats']);
        });
        
        // 系统监控
        Route::prefix('monitor')->group(function () {
            Route::get('health', [SystemMonitorController::class, 'getHealthStatus']);
            Route::get('performance', [SystemMonitorController::class, 'getPerformanceMetrics']);
            Route::get('alerts', [SystemMonitorController::class, 'getAlerts']);
            Route::get('suggestions', [SystemMonitorController::class, 'getOptimizationSuggestions']);
            Route::get('slow-queries', [SystemMonitorController::class, 'getSlowQueries']);
            Route::get('realtime', [SystemMonitorController::class, 'getRealTimeMetrics']);
            Route::get('events', [SystemMonitorController::class, 'getSystemEvents']);
            Route::get('configuration', [SystemMonitorController::class, 'getSystemConfiguration']);
            Route::post('check', [SystemMonitorController::class, 'runSystemCheck']);
        });
        
        // 统计数据
        Route::prefix('statistics')->group(function () {
            Route::get('overview', [StatisticsController::class, 'overview']);
            Route::get('users', [StatisticsController::class, 'userStats']);
            Route::get('groups', [StatisticsController::class, 'groupStats']);
            Route::get('orders', [StatisticsController::class, 'orderStats']);
            Route::get('revenue', [StatisticsController::class, 'revenueStats']);
            Route::get('distributors', [StatisticsController::class, 'distributorStats']);
            Route::get('export', [StatisticsController::class, 'exportStats']);
        });
        
        // 群组模板管理
        Route::prefix('group-templates')->group(function () {
            Route::get('/', [App\Http\Controllers\Api\GroupTemplateController::class, 'index']);
            Route::post('/', [App\Http\Controllers\Api\GroupTemplateController::class, 'store']);
            Route::get('/categories', [App\Http\Controllers\Api\GroupTemplateController::class, 'categories']);
            Route::get('/statistics', [App\Http\Controllers\Api\GroupTemplateController::class, 'statistics']);
            Route::post('/upload-cover', [App\Http\Controllers\Api\GroupTemplateController::class, 'uploadCover']);
            Route::post('/create-group', [App\Http\Controllers\Api\GroupTemplateController::class, 'createGroupFromTemplate']);
            Route::post('/batch-operation', [App\Http\Controllers\Api\GroupTemplateController::class, 'batchOperation']);
            Route::get('/{id}', [App\Http\Controllers\Api\GroupTemplateController::class, 'show']);
            Route::put('/{id}', [App\Http\Controllers\Api\GroupTemplateController::class, 'update']);
            Route::delete('/{id}', [App\Http\Controllers\Api\GroupTemplateController::class, 'destroy']);
            Route::put('/{id}/status', [App\Http\Controllers\Api\GroupTemplateController::class, 'toggleStatus']);
            Route::post('/{id}/copy', [App\Http\Controllers\Api\GroupTemplateController::class, 'copy']);
            Route::get('/{id}/preview', [App\Http\Controllers\Api\GroupTemplateController::class, 'preview']);
            Route::post('/export-group/{groupId}', [App\Http\Controllers\Api\GroupTemplateController::class, 'exportGroupAsTemplate']);
        });
        
        // 招募相关
        Route::prefix('recruit')->group(function() {
            Route::post('generate-invite-code', [DistributorController::class, 'generateInviteCode']);
            Route::get('invite-stats', [DistributorController::class, 'getInviteStats']);
            Route::get('invite-list', [DistributorController::class, 'getInviteList']);
            Route::get('promotion-materials', [DistributorController::class, 'getPromotionMaterials']);
            Route::post('set-target', [DistributorController::class, 'setRecruitTarget']);
        });
        
        // 支付日志管理（新增，对应前端调用）
        Route::prefix('payment')->group(function () {
            Route::get('logs', function() {
                return response()->json([
                    'code' => 200,
                    'message' => '获取成功',
                    'data' => [
                        'data' => [
                            [
                                'id' => 1,
                                'type' => 'callback',
                                'order_no' => 'ORDER_' . date('Ymd') . '001',
                                'payment_method' => 'wechat',
                                'amount' => '99.00',
                                'status' => 'success',
                                'created_at' => now()->subHours(2)->format('Y-m-d H:i:s'),
                                'updated_at' => now()->subHours(2)->addMinutes(5)->format('Y-m-d H:i:s'),
                                'request_data' => json_encode(['order_no' => 'ORDER_' . date('Ymd') . '001', 'amount' => 99.00, 'payment_method' => 'wechat'], JSON_PRETTY_PRINT),
                                'response_data' => json_encode(['code' => 0, 'message' => 'success', 'transaction_id' => 'wx123456789'], JSON_PRETTY_PRINT),
                                'error_message' => null
                            ],
                            [
                                'id' => 2,
                                'type' => 'payment_failed',
                                'order_no' => 'ORDER_' . date('Ymd') . '002',
                                'payment_method' => 'alipay',
                                'amount' => '199.00',
                                'status' => 'failed',
                                'created_at' => now()->subHours(3)->format('Y-m-d H:i:s'),
                                'updated_at' => now()->subHours(3)->addMinutes(10)->format('Y-m-d H:i:s'),
                                'request_data' => json_encode(['order_no' => 'ORDER_' . date('Ymd') . '002', 'amount' => 199.00, 'payment_method' => 'alipay'], JSON_PRETTY_PRINT),
                                'response_data' => json_encode(['code' => -1, 'message' => 'payment failed', 'error_code' => 'INSUFFICIENT_BALANCE'], JSON_PRETTY_PRINT),
                                'error_message' => '支付验证失败：余额不足'
                            ],
                            [
                                'id' => 3,
                                'type' => 'payment_success',
                                'order_no' => 'ORDER_' . date('Ymd') . '003',
                                'payment_method' => 'bank',
                                'amount' => '299.99',
                                'status' => 'success',
                                'created_at' => now()->subHours(4)->format('Y-m-d H:i:s'),
                                'updated_at' => now()->subHours(4)->addMinutes(15)->format('Y-m-d H:i:s'),
                                'request_data' => json_encode(['order_no' => 'ORDER_' . date('Ymd') . '003', 'amount' => 299.99, 'payment_method' => 'bank', 'card_no' => '****1234'], JSON_PRETTY_PRINT),
                                'response_data' => json_encode(['code' => 0, 'message' => 'success', 'transaction_id' => 'bank987654321', 'bank_code' => 'ABC'], JSON_PRETTY_PRINT),
                                'error_message' => null
                            ]
                        ],
                        'current_page' => (int) request('page', 1),
                        'per_page' => (int) request('per_page', 20),
                        'total' => 187,
                        'last_page' => 10
                    ]
                ]);
            });
            
            Route::get('logs/stats', function() {
                return response()->json([
                    'code' => 200,
                    'message' => '获取成功',
                    'data' => [
                        'success_count' => 156,
                        'failed_count' => 23,
                        'pending_count' => 8,
                        'total_count' => 187
                    ]
                ]);
            });
            
            Route::post('logs/export', function() {
                // 模拟导出Excel文件
                $filename = 'payment-logs-' . date('Y-m-d') . '.xlsx';
                $data = "ID,类型,订单号,支付方式,金额,状态,创建时间\n";
                $data .= "1,支付回调,ORDER_" . date('Ymd') . "001,微信支付,99.00,成功," . now()->format('Y-m-d H:i:s') . "\n";
                $data .= "2,支付失败,ORDER_" . date('Ymd') . "002,支付宝,199.00,失败," . now()->subHours(1)->format('Y-m-d H:i:s') . "\n";
                
                return response($data)
                    ->header('Content-Type', 'application/vnd.ms-excel')
                    ->header('Content-Disposition', 'attachment; filename="' . $filename . '"');
            });
        });
        
        // 分站模板管理
        Route::apiResource('substation-templates', SubstationTemplateController::class);
        Route::prefix('substation-templates')->group(function () {
            Route::post('{id}/clone', [SubstationTemplateController::class, 'clone']);
            Route::post('update-sort', [SubstationTemplateController::class, 'updateSort']);
            Route::get('categories', [SubstationTemplateController::class, 'categories']);
            Route::get('active-templates', [SubstationTemplateController::class, 'activeTemplates']);
            Route::get('{id}/preview', [SubstationTemplateController::class, 'preview']);
        });
    });
});

// 测试API（无需认证）
Route::prefix('api/test/payment')->group(function () {
    Route::get('logs', function() {
        return response()->json([
            'code' => 200,
            'message' => '测试成功 - 支付日志API工作正常',
            'data' => [
                'data' => [
                    [
                        'id' => 1,
                        'type' => 'callback',
                        'order_no' => 'TEST_ORDER_001',
                        'payment_method' => 'wechat',
                        'amount' => '99.00',
                        'status' => 'success',
                        'created_at' => now()->format('Y-m-d H:i:s'),
                        'updated_at' => now()->format('Y-m-d H:i:s'),
                        'request_data' => json_encode(['test' => true], JSON_PRETTY_PRINT),
                        'response_data' => json_encode(['success' => true], JSON_PRETTY_PRINT),
                        'error_message' => null
                    ]
                ],
                'total' => 1
            ]
        ]);
    });
    
    Route::get('logs/stats', function() {
        return response()->json([
            'code' => 200,
            'message' => '统计数据获取成功',
            'data' => [
                'success_count' => 1,
                'failed_count' => 0,
                'pending_count' => 0,
                'total_count' => 1
            ]
        ]);
    });
});

// V1 API 支付日志路由（确保兼容性）
Route::prefix('v1/admin/payment')->middleware(['auth:api', 'role:admin'])->group(function () {
    Route::get('logs', function() {
        return response()->json([
            'code' => 200,
            'message' => '获取成功',
            'data' => [
                'data' => [
                    [
                        'id' => 1,
                        'type' => 'callback',
                        'order_no' => 'ORDER_' . date('Ymd') . '001',
                        'payment_method' => 'wechat',
                        'amount' => '99.00',
                        'status' => 'success',
                        'created_at' => now()->subHours(2)->format('Y-m-d H:i:s'),
                        'updated_at' => now()->subHours(2)->addMinutes(5)->format('Y-m-d H:i:s'),
                        'request_data' => json_encode(['order_no' => 'ORDER_' . date('Ymd') . '001', 'amount' => 99.00, 'payment_method' => 'wechat'], JSON_PRETTY_PRINT),
                        'response_data' => json_encode(['code' => 0, 'message' => 'success', 'transaction_id' => 'wx123456789'], JSON_PRETTY_PRINT),
                        'error_message' => null
                    ],
                    [
                        'id' => 2,
                        'type' => 'payment_failed',
                        'order_no' => 'ORDER_' . date('Ymd') . '002',
                        'payment_method' => 'alipay',
                        'amount' => '199.00',
                        'status' => 'failed',
                        'created_at' => now()->subHours(3)->format('Y-m-d H:i:s'),
                        'updated_at' => now()->subHours(3)->addMinutes(10)->format('Y-m-d H:i:s'),
                        'request_data' => json_encode(['order_no' => 'ORDER_' . date('Ymd') . '002', 'amount' => 199.00, 'payment_method' => 'alipay'], JSON_PRETTY_PRINT),
                        'response_data' => json_encode(['code' => -1, 'message' => 'payment failed', 'error_code' => 'INSUFFICIENT_BALANCE'], JSON_PRETTY_PRINT),
                        'error_message' => '支付验证失败：余额不足'
                    ]
                ],
                'current_page' => (int) request('page', 1),
                'per_page' => (int) request('per_page', 20),
                'total' => 187,
                'last_page' => 10
            ]
        ]);
    });
    
    Route::get('logs/stats', function() {
        return response()->json([
            'code' => 200,
            'message' => '获取成功',
            'data' => [
                'success_count' => 156,
                'failed_count' => 23,
                'pending_count' => 8,
                'total_count' => 187
            ]
        ]);
    });
    
    Route::post('logs/export', function() {
        $filename = 'payment-logs-' . date('Y-m-d') . '.xlsx';
        $data = "ID,类型,订单号,支付方式,金额,状态,创建时间\n";
        $data .= "1,支付回调,ORDER_" . date('Ymd') . "001,微信支付,99.00,成功," . now()->format('Y-m-d H:i:s') . "\n";
        $data .= "2,支付失败,ORDER_" . date('Ymd') . "002,支付宝,199.00,失败," . now()->subHours(1)->format('Y-m-d H:i:s') . "\n";
        
        return response($data)
            ->header('Content-Type', 'application/vnd.ms-excel')
            ->header('Content-Disposition', 'attachment; filename="' . $filename . '"');
    });
});

// 分站管理员路由
Route::prefix('v1/substation')->middleware(['auth:sanctum', 'role:substation'])->group(function () {
    Route::get('dashboard', [DashboardController::class, 'substationDashboard']);
    Route::get('users', [UserManagementController::class, 'substationUsers']);
    Route::get('groups', [WechatGroupController::class, 'substationGroups']);
    Route::get('orders', [OrderController::class, 'substationOrders']);
    Route::get('distributors', [DistributorController::class, 'substationDistributors']);
    Route::get('statistics', [StatisticsController::class, 'substationStats']);
    Route::apiResource('substations', SubstationController::class);
    
    // 分站增强功能路由
    Route::prefix('substations')->group(function () {
        Route::get('stats', [SubstationController::class, 'getStats']);
        Route::post('batch-update', [SubstationController::class, 'batchUpdate']);
        Route::post('create-from-template', [SubstationController::class, 'createFromTemplate']);
        Route::post('export', [SubstationController::class, 'export']);
        Route::post('clear-cache', [SubstationController::class, 'clearCache']);
    });
    
    // 分站告警管理路由
    Route::prefix('alerts')->group(function () {
        Route::get('/', [SubstationAlertController::class, 'index']);
        Route::get('statistics', [SubstationAlertController::class, 'statistics']);
        Route::get('trends', [SubstationAlertController::class, 'trends']);
        Route::get('{id}', [SubstationAlertController::class, 'show']);
        Route::post('{id}/acknowledge', [SubstationAlertController::class, 'acknowledge']);
        Route::post('{id}/resolve', [SubstationAlertController::class, 'resolve']);
        Route::post('{id}/ignore', [SubstationAlertController::class, 'ignore']);
        Route::post('batch-handle', [SubstationAlertController::class, 'batchHandle']);
        Route::post('check', [SubstationAlertController::class, 'checkAlerts']);
    });
    
    // 分站数据分析路由
    Route::prefix('analytics')->group(function () {
        Route::get('comprehensive-report', [SubstationAnalyticsController::class, 'getComprehensiveReport']);
        Route::get('overview-stats', [SubstationAnalyticsController::class, 'getOverviewStats']);
        Route::get('user-analytics', [SubstationAnalyticsController::class, 'getUserAnalytics']);
        Route::get('group-analytics', [SubstationAnalyticsController::class, 'getGroupAnalytics']);
        Route::get('financial-analytics', [SubstationAnalyticsController::class, 'getFinancialAnalytics']);
        Route::get('performance-metrics', [SubstationAnalyticsController::class, 'getPerformanceMetrics']);
        Route::get('trend-analysis', [SubstationAnalyticsController::class, 'getTrendAnalysis']);
        Route::get('rankings', [SubstationAnalyticsController::class, 'getRankings']);
        Route::post('export-report', [SubstationAnalyticsController::class, 'exportReport']);
        Route::post('clear-cache', [SubstationAnalyticsController::class, 'clearCache']);
    });
    
    // 分站财务管理
    Route::prefix('finance')->group(function () {
        Route::get('stats', [SubstationFinanceController::class, 'getStats']);
        Route::post('report', [SubstationFinanceController::class, 'generateReport']);
        Route::get('settlements', [SubstationFinanceController::class, 'getSettlementRecords']);
        Route::post('batch-settle', [SubstationFinanceController::class, 'batchSettle']);
        Route::get('revenue-trend', [SubstationFinanceController::class, 'getRevenueTrend']);
        Route::get('commission-analysis', [SubstationFinanceController::class, 'getCommissionAnalysis']);
        Route::get('top-agents', [SubstationFinanceController::class, 'getTopAgents']);
        Route::post('export', [SubstationFinanceController::class, 'exportData']);
    });
    
    // 代理商申请管理
    Route::prefix('agent-applications')->group(function () {
        Route::get('/', [AgentApplicationController::class, 'index']);
        Route::get('stats', [AgentApplicationController::class, 'getStats']);
        Route::get('{id}', [AgentApplicationController::class, 'show']);
        Route::post('{id}/review', [AgentApplicationController::class, 'review']);
        Route::post('batch-review', [AgentApplicationController::class, 'batchReview']);
    });
});

// 高级群组管理路由
Route::middleware(['auth:sanctum'])->prefix('v1/groups/advanced')->group(function () {
    Route::get('{groupId}/health', [App\Http\Controllers\Api\AdvancedGroupController::class, 'getGroupHealth']);
    Route::get('{groupId}/monitoring', [App\Http\Controllers\Api\AdvancedGroupController::class, 'getPerformanceMonitoring']);
    Route::post('{groupId}/optimize', [App\Http\Controllers\Api\AdvancedGroupController::class, 'autoOptimizeGroup']);
    Route::get('{groupId}/pricing-recommendation', [App\Http\Controllers\Api\AdvancedGroupController::class, 'getSmartPricingRecommendation']);
    Route::get('recommendations', [App\Http\Controllers\Api\AdvancedGroupController::class, 'getPersonalizedRecommendations']);
    Route::get('search', [App\Http\Controllers\Api\AdvancedGroupController::class, 'getPersonalizedSearch']);
    Route::post('ab-tests', [App\Http\Controllers\Api\AdvancedGroupController::class, 'createABTest']);
    Route::get('ab-tests/{testId}/results', [App\Http\Controllers\Api\AdvancedGroupController::class, 'getABTestResults']);
    Route::post('ab-tests/{testId}/stop', [App\Http\Controllers\Api\AdvancedGroupController::class, 'stopABTest']);
    Route::get('ab-tests/templates', [App\Http\Controllers\Api\AdvancedGroupController::class, 'getABTestTemplates']);
    Route::get('templates', [App\Http\Controllers\Api\AdvancedGroupController::class, 'getGroupTemplates']);
    Route::post('templates/create', [App\Http\Controllers\Api\AdvancedGroupController::class, 'createGroupFromTemplate']);
    Route::post('templates/batch-create', [App\Http\Controllers\Api\AdvancedGroupController::class, 'batchCreateGroups']);
    Route::post('{groupId}/export-template', [App\Http\Controllers\Api\AdvancedGroupController::class, 'exportGroupAsTemplate']);
    Route::get('templates/recommended', [App\Http\Controllers\Api\AdvancedGroupController::class, 'getRecommendedTemplates']);
    Route::get('quick-create-configs', [App\Http\Controllers\Api\AdvancedGroupController::class, 'getQuickCreateConfigs']);
    Route::get('dashboard', [App\Http\Controllers\Api\AdvancedGroupController::class, 'getDashboard']);
    Route::get('analytics', [App\Http\Controllers\Api\AdvancedGroupController::class, 'getAnalyticsReport']);
});

// V1 API 兼容路由
Route::prefix('v1')->group(function () {
    Route::get('/', function () {
        return response()->json([
            'success' => true,
            'message' => 'API v1 Ready',
            'version' => '1.0',
            'timestamp' => now()->toDateTimeString(),
        ]);
    });
    
    Route::get('test/simple', function () {
        return response()->json([
            'success' => true,
            'message' => 'API v1 测试成功',
            'data' => [
                'timestamp' => now()->toDateTimeString(),
                'path' => request()->path(),
                'full_url' => request()->fullUrl(),
            ],
        ]);
    });

    // 测试订单API（无认证）
    Route::get('test/orders', function() {
        return response()->json([
            'code' => 200,
            'message' => '订单API测试成功',
            'data' => [
                'timestamp' => now()->toDateTimeString(),
                'url' => request()->fullUrl(),
                'orders' => [
                    [
                        'id' => 1,
                        'order_no' => 'TEST_ORDER_001',
                        'amount' => '99.00',
                        'status' => 'paid'
                    ]
                ]
            ]
        ]);
    });

    // 订单管理API路由（管理员）
    Route::prefix('admin/orders')->middleware(['auth:api', 'role:admin'])->group(function () {
        Route::get('/', function() {
            $page = request('page', 1);
            $limit = request('limit', 20);
            $keyword = request('keyword', '');
            $status = request('status', '');
            $payment_method = request('payment_method', '');
            $start_date = request('start_date', '');
            $end_date = request('end_date', '');
            
            return response()->json([
                'code' => 200,
                'message' => '获取成功',
                'data' => [
                    'data' => [
                        [
                            'id' => 1,
                            'order_no' => 'ORDER_' . date('Ymd') . '001',
                            'user_id' => 1001,
                            'username' => '用户001',
                            'group_name' => '北京高端商务群',
                            'amount' => '99.00',
                            'payment_method' => 'wechat',
                            'status' => 'paid',
                            'created_at' => now()->subHours(2)->format('Y-m-d H:i:s'),
                            'updated_at' => now()->subHours(1)->format('Y-m-d H:i:s'),
                            'paid_at' => now()->subHours(1)->format('Y-m-d H:i:s')
                        ],
                        [
                            'id' => 2,
                            'order_no' => 'ORDER_' . date('Ymd') . '002',
                            'user_id' => 1002,
                            'username' => '用户002',
                            'group_name' => '上海投资理财群',
                            'amount' => '199.00',
                            'payment_method' => 'alipay',
                            'status' => 'pending',
                            'created_at' => now()->subHours(3)->format('Y-m-d H:i:s'),
                            'updated_at' => now()->subHours(3)->format('Y-m-d H:i:s'),
                            'paid_at' => null
                        ],
                        [
                            'id' => 3,
                            'order_no' => 'ORDER_' . date('Ymd') . '003',
                            'user_id' => 1003,
                            'username' => '用户003',
                            'group_name' => '深圳科技创业群',
                            'amount' => '299.00',
                            'payment_method' => 'bank',
                            'status' => 'refunded',
                            'created_at' => now()->subHours(5)->format('Y-m-d H:i:s'),
                            'updated_at' => now()->subMinutes(30)->format('Y-m-d H:i:s'),
                            'paid_at' => now()->subHours(4)->format('Y-m-d H:i:s')
                        ]
                    ],
                    'current_page' => (int) $page,
                    'per_page' => (int) $limit,
                    'total' => 1567,
                    'last_page' => ceil(1567 / $limit)
                ]
            ]);
        });
        
        Route::get('stats', function() {
            return response()->json([
                'code' => 200,
                'message' => '获取成功',
                'data' => [
                    'total_orders' => 1567,
                    'paid_orders' => 1234,
                    'pending_orders' => 245,
                    'refunded_orders' => 88,
                    'total_amount' => 234567.89,
                    'today_orders' => 45,
                    'today_amount' => 8901.23
                ]
            ]);
        });
        
        Route::get('{id}', function($id) {
            return response()->json([
                'code' => 200,
                'message' => '获取成功',
                'data' => [
                    'id' => (int) $id,
                    'order_no' => 'ORDER_' . date('Ymd') . str_pad($id, 3, '0', STR_PAD_LEFT),
                    'user_id' => 1000 + $id,
                    'username' => '用户' . str_pad($id, 3, '0', STR_PAD_LEFT),
                    'group_id' => 100 + $id,
                    'group_name' => '测试群组' . $id,
                    'amount' => number_format((99 + $id * 10), 2),
                    'payment_method' => ['wechat', 'alipay', 'bank'][($id - 1) % 3],
                    'status' => ['paid', 'pending', 'refunded'][($id - 1) % 3],
                    'remark' => '订单备注信息',
                    'created_at' => now()->subHours($id)->format('Y-m-d H:i:s'),
                    'updated_at' => now()->subHours($id - 1)->format('Y-m-d H:i:s'),
                    'paid_at' => $id % 3 === 1 ? now()->subHours($id - 1)->format('Y-m-d H:i:s') : null
                ]
            ]);
        });
        
        Route::put('{id}', function($id) {
            return response()->json([
                'code' => 200,
                'message' => '更新成功',
                'data' => [
                    'id' => (int) $id,
                    'updated_at' => now()->format('Y-m-d H:i:s')
                ]
            ]);
        });
        
        Route::delete('{id}', function($id) {
            return response()->json([
                'code' => 200,
                'message' => '删除成功'
            ]);
        });
        
        Route::post('batch-process', function() {
            return response()->json([
                'code' => 200,
                'message' => '批量处理成功',
                'data' => [
                    'processed_count' => count(request('order_ids', [])),
                    'success_count' => count(request('order_ids', [])),
                    'failed_count' => 0
                ]
            ]);
        });
        
        Route::post('{id}/refund', function($id) {
            return response()->json([
                'code' => 200,
                'message' => '退款申请已提交',
                'data' => [
                    'refund_id' => 'REFUND_' . date('Ymd') . str_pad($id, 3, '0', STR_PAD_LEFT),
                    'status' => 'processing'
                ]
            ]);
        });
        
        Route::post('{id}/cancel', function($id) {
            return response()->json([
                'code' => 200,
                'message' => '订单已取消'
            ]);
        });
        
        Route::get('statistics', function() {
            return response()->json([
                'code' => 200,
                'message' => '获取成功',
                'data' => [
                    'daily_stats' => [
                        ['date' => date('Y-m-d', strtotime('-6 days')), 'orders' => 45, 'amount' => 4500.00],
                        ['date' => date('Y-m-d', strtotime('-5 days')), 'orders' => 52, 'amount' => 5200.00],
                        ['date' => date('Y-m-d', strtotime('-4 days')), 'orders' => 38, 'amount' => 3800.00],
                        ['date' => date('Y-m-d', strtotime('-3 days')), 'orders' => 61, 'amount' => 6100.00],
                        ['date' => date('Y-m-d', strtotime('-2 days')), 'orders' => 47, 'amount' => 4700.00],
                        ['date' => date('Y-m-d', strtotime('-1 days')), 'orders' => 55, 'amount' => 5500.00],
                        ['date' => date('Y-m-d'), 'orders' => 43, 'amount' => 4300.00]
                    ],
                    'payment_method_stats' => [
                        ['method' => 'wechat', 'count' => 856, 'amount' => 85600.00, 'percentage' => 60.2],
                        ['method' => 'alipay', 'count' => 432, 'amount' => 43200.00, 'percentage' => 30.4],
                        ['method' => 'bank', 'count' => 134, 'amount' => 13400.00, 'percentage' => 9.4]
                    ],
                    'status_stats' => [
                        ['status' => 'paid', 'count' => 1234, 'percentage' => 78.7],
                        ['status' => 'pending', 'count' => 245, 'percentage' => 15.6],
                        ['status' => 'refunded', 'count' => 88, 'percentage' => 5.7]
                    ]
                ]
            ]);
        });
    });

    // 其他V1兼容路由可以在这里添加
});

// 测试路由
Route::get('api/v1/test/response', [TestController::class, 'testResponse']);

// 提现管理演示路由
Route::prefix('v1/demo/withdrawal')->group(function () {
    Route::post('create-data', [App\Http\Controllers\Api\WithdrawalDemoController::class, 'createDemoData']);
    Route::get('dashboard', [App\Http\Controllers\Api\WithdrawalDemoController::class, 'getDashboard']);
    Route::get('manual-transfer', [App\Http\Controllers\Api\WithdrawalDemoController::class, 'demoManualTransfer']);
    Route::get('auto-transfer', [App\Http\Controllers\Api\WithdrawalDemoController::class, 'demoAutoTransfer']);
    Route::get('monitoring', [App\Http\Controllers\Api\WithdrawalDemoController::class, 'demoMonitoring']);
    Route::delete('clean-data', [App\Http\Controllers\Api\WithdrawalDemoController::class, 'cleanDemoData']);
});

// 支付管理API路由（适配前端调用）
Route::prefix('admin/payment')->group(function () {
    // 支付渠道管理
    Route::prefix('channels')->group(function () {
        Route::get('overview', function() {
            return response()->json([
                'code' => 200,
                'message' => '获取成功',
                'data' => [
                    'total_channels' => 8,
                    'active_channels' => 6,
                    'active_configs' => 12,
                    'total_users' => 156
                ]
            ]);
        });
        
        Route::get('/', function() {
            return response()->json([
                'code' => 200,
                'message' => '获取成功',
                'data' => [
                    [
                        'id' => 1,
                        'channel_code' => 'alipay',
                        'channel_name' => '支付宝',
                        'channel_type' => 'third_party',
                        'status' => true,
                        'config_count' => 3,
                        'user_count' => 45,
                        'description' => '支付宝第三方支付接口',
                        'created_at' => '2024-01-15 10:30:00',
                        'updated_at' => now()->format('Y-m-d H:i:s')
                    ],
                    [
                        'id' => 2,
                        'channel_code' => 'wechat',
                        'channel_name' => '微信支付',
                        'channel_type' => 'third_party',
                        'status' => true,
                        'config_count' => 2,
                        'user_count' => 38,
                        'description' => '微信支付接口',
                        'created_at' => '2024-01-15 10:35:00',
                        'updated_at' => now()->format('Y-m-d H:i:s')
                    ],
                    [
                        'id' => 3,
                        'channel_code' => 'easypay',
                        'channel_name' => '易支付',
                        'channel_type' => 'gateway',
                        'status' => false,
                        'config_count' => 1,
                        'user_count' => 0,
                        'description' => '易支付网关',
                        'created_at' => '2024-02-10 09:20:00',
                        'updated_at' => now()->format('Y-m-d H:i:s')
                    ]
                ]
            ]);
        });
    });
});

// 直接支付日志API路由（适配前端调用）
Route::prefix('v1/admin/payment')->group(function () {
    Route::get('transfer-logs', function() {
        $page = request('page', 1);
        $perPage = request('per_page', 10);
        
        return response()->json([
            'code' => 200,
            'message' => '获取成功',
            'data' => [
                'data' => [
                    [
                        'id' => 1,
                        'type' => 'callback',
                        'order_no' => 'ORDER_' . date('Ymd') . '001',
                        'payment_method' => 'wechat',
                        'amount' => '99.00',
                        'status' => 'success',
                        'created_at' => now()->subHours(2)->format('Y-m-d H:i:s'),
                        'updated_at' => now()->subHours(2)->addMinutes(5)->format('Y-m-d H:i:s'),
                        'request_data' => json_encode(['order_no' => 'ORDER_' . date('Ymd') . '001', 'amount' => 99.00, 'payment_method' => 'wechat'], JSON_PRETTY_PRINT),
                        'response_data' => json_encode(['code' => 0, 'message' => 'success', 'transaction_id' => 'wx123456789'], JSON_PRETTY_PRINT),
                        'error_message' => null
                    ],
                    [
                        'id' => 2,
                        'type' => 'payment_failed',
                        'order_no' => 'ORDER_' . date('Ymd') . '002',
                        'payment_method' => 'alipay',
                        'amount' => '199.00',
                        'status' => 'failed',
                        'created_at' => now()->subHours(3)->format('Y-m-d H:i:s'),
                        'updated_at' => now()->subHours(3)->addMinutes(10)->format('Y-m-d H:i:s'),
                        'request_data' => json_encode(['order_no' => 'ORDER_' . date('Ymd') . '002', 'amount' => 199.00, 'payment_method' => 'alipay'], JSON_PRETTY_PRINT),
                        'response_data' => json_encode(['code' => -1, 'message' => 'payment failed', 'error_code' => 'INSUFFICIENT_BALANCE'], JSON_PRETTY_PRINT),
                        'error_message' => '支付验证失败：余额不足'
                    ]
                ],
                'current_page' => (int) $page,
                'per_page' => (int) $perPage,
                'total' => 187,
                'last_page' => ceil(187 / $perPage)
            ]
        ]);
    });
    
    Route::get('config', function() {
        return response()->json([
            'code' => 200,
            'message' => '获取成功',
            'data' => [
                'alipay_enabled' => true,
                'wechat_enabled' => true,
                'bank_enabled' => false,
                'auto_withdraw_enabled' => false
            ]
        ]);
    });
});

// 测试API（无需认证）- 放在fallback路由之前
Route::prefix('api/test/payment')->group(function () {
    Route::get('logs', function() {
        return response()->json([
            'code' => 200,
            'message' => '测试成功 - 支付日志API工作正常',
            'data' => [
                'data' => [
                    [
                        'id' => 1,
                        'type' => 'callback',
                        'order_no' => 'TEST_ORDER_001',
                        'payment_method' => 'wechat',
                        'amount' => '99.00',
                        'status' => 'success',
                        'created_at' => now()->format('Y-m-d H:i:s'),
                        'updated_at' => now()->format('Y-m-d H:i:s'),
                        'request_data' => json_encode(['test' => true], JSON_PRETTY_PRINT),
                        'response_data' => json_encode(['success' => true], JSON_PRETTY_PRINT),
                        'error_message' => null
                    ]
                ],
                'total' => 1
            ]
        ]);
    });
    
    Route::get('logs/stats', function() {
        return response()->json([
            'code' => 200,
            'message' => '统计数据获取成功',
            'data' => [
                'success_count' => 1,
                'failed_count' => 0,
                'pending_count' => 0,
                'total_count' => 1
            ]
        ]);
    });
});

// 支付渠道管理API路由（放在fallback之前）
Route::prefix('admin/payment/channels')->group(function () {
    Route::get('overview', function() {
        return response()->json([
            'code' => 200,
            'message' => '获取成功',
            'data' => [
                'total_channels' => 8,
                'active_channels' => 6,
                'active_configs' => 12,
                'total_users' => 156
            ]
        ]);
    });
    
    Route::get('/', function() {
        return response()->json([
            'code' => 200,
            'message' => '获取成功',
            'data' => [
                [
                    'id' => 1,
                    'channel_code' => 'alipay',
                    'channel_name' => '支付宝',
                    'channel_type' => 'third_party',
                    'status' => true,
                    'config_count' => 3,
                    'user_count' => 45,
                    'description' => '支付宝第三方支付接口',
                    'created_at' => '2024-01-15 10:30:00',
                    'updated_at' => now()->format('Y-m-d H:i:s')
                ],
                [
                    'id' => 2,
                    'channel_code' => 'wechat',
                    'channel_name' => '微信支付',
                    'channel_type' => 'third_party',
                    'status' => true,
                    'config_count' => 2,
                    'user_count' => 38,
                    'description' => '微信支付接口',
                    'created_at' => '2024-01-15 10:35:00',
                    'updated_at' => now()->format('Y-m-d H:i:s')
                ],
                [
                    'id' => 3,
                    'channel_code' => 'easypay',
                    'channel_name' => '易支付',
                    'channel_type' => 'gateway',
                    'status' => false,
                    'config_count' => 1,
                    'user_count' => 0,
                    'description' => '易支付网关',
                    'created_at' => '2024-02-10 09:20:00',
                    'updated_at' => now()->format('Y-m-d H:i:s')
                ]
            ]
        ]);
    });
});

// 404 API 路由
Route::fallback(function () {
    return response()->json([
        'error' => 'API route not found',
        'message' => 'The requested API endpoint does not exist'
    ], 404);
});
// 分销员客户管理路由
Route::middleware(['auth:api', 'role:distributor'])->group(function () {
    Route::prefix('distributor/customers')->group(function () {
        Route::get('/', [App\Http\Controllers\Api\DistributorCustomerController::class, 'index']);
        Route::post('/', [App\Http\Controllers\Api\DistributorCustomerController::class, 'store']);
        Route::get('stats', [App\Http\Controllers\Api\DistributorCustomerController::class, 'getStats']);
        Route::get('need-follow-up', [App\Http\Controllers\Api\DistributorCustomerController::class, 'getNeedFollowUp']);
        Route::get('tags', [App\Http\Controllers\Api\DistributorCustomerController::class, 'getTags']);
        Route::get('export', [App\Http\Controllers\Api\DistributorCustomerController::class, 'export']);
        Route::post('batch-status', [App\Http\Controllers\Api\DistributorCustomerController::class, 'batchUpdateStatus']);
        
        Route::get('{id}', [App\Http\Controllers\Api\DistributorCustomerController::class, 'show']);
        Route::put('{id}', [App\Http\Controllers\Api\DistributorCustomerController::class, 'update']);
        Route::delete('{id}', [App\Http\Controllers\Api\DistributorCustomerController::class, 'destroy']);
        
        // 跟进记录相关
        Route::post('{id}/follow-ups', [App\Http\Controllers\Api\DistributorCustomerController::class, 'addFollowUp']);
        Route::get('{id}/follow-ups', [App\Http\Controllers\Api\DistributorCustomerController::class, 'getFollowUps']);
    });
});

// 增强分站权限管理路由
Route::middleware(['auth:api', 'substation.permission:user_management'])->group(function () {
    Route::prefix('substation/permissions')->group(function () {
        Route::get('config', [App\Http\Controllers\Api\SubstationPermissionController::class, 'getConfig']);
        Route::post('config', [App\Http\Controllers\Api\SubstationPermissionController::class, 'updateConfig']);
        Route::get('available', [App\Http\Controllers\Api\SubstationPermissionController::class, 'getAvailablePermissions']);
    });
});

// 代理商API (需要认证)
Route::middleware('auth:sanctum')->prefix('admin')->group(function () {
    // 代理商管理路由
    Route::prefix('agents')->group(function () {
        // 获取代理商列表
        Route::get('/', [AgentController::class, 'index']);
        
        // 层级结构相关 (必须在 {id} 路由之前)
        Route::get('hierarchy', [AgentController::class, 'getHierarchy']);
        Route::get('hierarchy/stats', [AgentController::class, 'getHierarchyStats']);
        Route::get('available-parents', [AgentController::class, 'getAvailableParents']);
        
        // 代理商状态管理
        Route::post('batch/status', [AgentController::class, 'batchUpdateStatus']);
        
        // 获取代理商详情 (放在具体路由之后)
        Route::get('{id}', [AgentController::class, 'show']);
        // 创建代理商
        Route::post('/', [AgentController::class, 'store']);
        // 更新代理商
        Route::put('{id}', [AgentController::class, 'update']);
        // 删除代理商
        Route::delete('{id}', [AgentController::class, 'destroy']);
        
        // 代理商状态管理
        Route::put('{id}/status', [AgentController::class, 'updateStatus']);
        
        // 代理商业务数据
        Route::get('{id}/commissions', [AgentController::class, 'getCommissions']);
        Route::get('{id}/performance', [AgentController::class, 'getPerformance']);
        Route::post('{id}/transfer', [AgentController::class, 'transfer']);
        
        // 我的代理商信息
        Route::get('my', [AgentController::class, 'getMy']);
        Route::get('my-stats', [AgentController::class, 'getMyStats']);
        Route::put('my', [AgentController::class, 'updateMy']);
        
        // 数据导出
        Route::get('export', [AgentController::class, 'export']);
    });
});

// 城市定位API
Route::prefix('location')->group(function () {
    Route::get('ip', [App\Http\Controllers\Api\LocationController::class, 'getLocationByIP']);
    Route::post('reverse', [App\Http\Controllers\Api\LocationController::class, 'reverseGeocode']);
    Route::get('cities', [App\Http\Controllers\Api\LocationController::class, 'getCities']);
    Route::get('recommend', [App\Http\Controllers\Api\LocationController::class, 'recommendCities']);
    Route::post('batch', [App\Http\Controllers\Api\LocationController::class, 'batchLocation']);
});

// 群组营销功能API
Route::prefix('groups/{id}')->middleware('auth:api')->group(function () {
    Route::get('marketing-config', [WechatGroupController::class, 'getMarketingConfig']);
    Route::put('marketing-config', [WechatGroupController::class, 'updateMarketingConfig']);
    Route::post('virtual-members', [WechatGroupController::class, 'generateVirtualMembers']);
    Route::get('preview', [WechatGroupController::class, 'previewGroup']);
    Route::post('test-city', [WechatGroupController::class, 'testCityLocation']);
    Route::post('apply-template', [WechatGroupController::class, 'applyMarketingTemplate']);
});

// 营销模板API
Route::get('marketing-templates', [WechatGroupController::class, 'getMarketingTemplates']);
Route::post('groups/batch-marketing', [WechatGroupController::class, 'batchUpdateMarketing'])->middleware('auth:api');

// 防封系统增强API
Route::prefix('anti-block')->group(function () {
    Route::get('domain-health', [AntiBlockController::class, 'getDomainHealth']);
    Route::post('check-domain', [AntiBlockController::class, 'checkDomainHealth']);
    Route::get('browser-stats', [AntiBlockController::class, 'getBrowserStats']);
    Route::post('validate-access/{groupId}', [AntiBlockController::class, 'validateGroupAccess']);
    Route::get('access-report/{groupId}', [AntiBlockController::class, 'getAccessReport']);
});

// V1 API 防封系统增强API（与前端API调用匹配）
Route::prefix('v1/anti-block')->group(function () {
    Route::get('domain-health', [AntiBlockController::class, 'getDomainHealth']);
    Route::post('check-domain', [AntiBlockController::class, 'checkDomainHealth']);
    Route::get('browser-stats', [AntiBlockController::class, 'getBrowserStats']);
    Route::post('validate-access/{groupId}', [AntiBlockController::class, 'validateGroupAccess']);
    Route::get('access-report/{groupId}', [AntiBlockController::class, 'getAccessReport']);
});

// 群组落地页和支付相关路由（公开访问）
Route::prefix('group')->group(function () {
    Route::get('{slug}', [\App\Http\Controllers\GroupLandingController::class, 'landing'])->name('group.landing');
    Route::post('create-order', [\App\Http\Controllers\GroupLandingController::class, 'createOrder']);
    Route::post('query-order-status', [\App\Http\Controllers\GroupLandingController::class, 'queryOrderStatus']);
    Route::get('success/{orderNo}', [\App\Http\Controllers\GroupLandingController::class, 'successPage'])->name('group.success');
    Route::post('{groupId}/generate-promotion-link', [\App\Http\Controllers\GroupLandingController::class, 'generatePromotionLink']);
});

// 群组创建相关路由
Route::prefix('v1/admin')->middleware(['auth:api', 'admin'])->group(function () {
    // 群组管理
    Route::apiResource('groups', \App\Http\Controllers\Api\Admin\GroupController::class);
    Route::patch('groups/{id}/status', [\App\Http\Controllers\Api\Admin\GroupController::class, 'toggleStatus']);
    Route::get('groups/{id}/stats', [\App\Http\Controllers\Api\Admin\GroupController::class, 'stats']);
    Route::get('groups/{id}/members', [\App\Http\Controllers\Api\Admin\GroupController::class, 'members']);
    Route::delete('groups/{groupId}/members/{userId}', [\App\Http\Controllers\Api\Admin\GroupController::class, 'removeMember']);
    Route::get('groups/{id}/orders', [\App\Http\Controllers\Api\Admin\GroupController::class, 'orders']);
    Route::post('groups/batch-delete', [\App\Http\Controllers\Api\Admin\GroupController::class, 'batchDelete']);
    Route::post('groups/preview', [\App\Http\Controllers\Api\Admin\GroupController::class, 'preview']);
    Route::post('groups/{id}/poster', [\App\Http\Controllers\Api\Admin\GroupController::class, 'generatePoster']);
    Route::post('groups/{id}/duplicate', [\App\Http\Controllers\Api\Admin\GroupController::class, 'duplicate']);
    Route::get('groups/{id}/export', [\App\Http\Controllers\Api\Admin\GroupController::class, 'export']);
    Route::get('groups/{id}/analytics', [\App\Http\Controllers\Api\Admin\GroupController::class, 'analytics']);
    Route::post('groups/test-city-replacement', [\App\Http\Controllers\Api\Admin\GroupController::class, 'testCityReplacement']);
    Route::get('groups/recommended-settings', [\App\Http\Controllers\Api\Admin\GroupController::class, 'recommendedSettings']);

    // 群组推广链接生成 - 修复路由
    Route::post('groups/{groupId}/promotion-link', [\App\Http\Controllers\Api\Admin\AntiBlockController::class, 'generateGroupPromotionLink'])->name('groups.promotion-link');

    // 简单测试路由 - 验证路由是否工作
    Route::post('test/simple/{groupId}', function($groupId) {
        return response()->json([
            'success' => true,
            'code' => 200,
            'message' => '简单测试成功',
            'data' => [
                'group_id' => $groupId,
                'method' => 'POST',
                'timestamp' => now()->toISOString()
            ]
        ]);
    });

    // 测试防红推广链接路由
    Route::post('test/promotion-link/{groupId}', function($groupId) {
        return response()->json([
            'success' => true,
            'code' => 200,
            'message' => '防红推广链接测试成功',
            'data' => [
                'group_id' => $groupId,
                'method' => 'POST',
                'route' => 'test.promotion-link',
                'timestamp' => now()->toISOString()
            ]
        ]);
    });

    // 临时测试路由
    Route::get('test/promotion-link/{groupId}', function($groupId) {
        return response()->json([
            'success' => true,
            'code' => 200,
            'message' => '测试成功',
            'data' => [
                'group_id' => $groupId,
                'original_url' => "https://example.com/landing/group/{$groupId}",
                'anti_block_url' => "https://safe-domain.com/g/test123",
                'short_url' => "https://t.cn/TestABC",
                'qr_code_url' => "https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=" . urlencode("https://t.cn/TestABC"),
                'anti_block_enabled' => true,
                'short_link_enabled' => true,
                'created_at' => now()->toISOString()
            ]
        ]);
    });

    // 超级群组创建（增强版）
    Route::prefix('ultra-groups')->group(function () {
        Route::post('create', [\App\Http\Controllers\Api\UltraGroupController::class, 'createSuperGroup']);
        Route::get('test-location', [\App\Http\Controllers\Api\UltraGroupController::class, 'testLocation']);
        Route::get('test-virtual-data', [\App\Http\Controllers\Api\UltraGroupController::class, 'testVirtualData']);
        Route::get('test-anti-block', [\App\Http\Controllers\Api\UltraGroupController::class, 'testAntiBlock']);
        Route::post('batch-create', [\App\Http\Controllers\Api\UltraGroupController::class, 'batchCreateGroups']);
        Route::get('system-status', [\App\Http\Controllers\Api\UltraGroupController::class, 'getSystemStatus']);
    });
    
    // 支付配置测试
    Route::post('payment/test', [\App\Http\Controllers\Api\Admin\PaymentTestController::class, 'test']);
    Route::get('payment/status', [\App\Http\Controllers\Api\Admin\PaymentTestController::class, 'status']);
    
    // 域名池管理
    Route::apiResource('domain-pools', \App\Http\Controllers\Api\Admin\DomainPoolController::class);
    
    // 群组模板管理
    Route::apiResource('group-templates', \App\Http\Controllers\Api\Admin\GroupTemplateController::class);
    Route::post('group-templates/{id}/apply', [\App\Http\Controllers\Api\Admin\GroupTemplateController::class, 'apply']);

    // === 高级分析路由组 ===
    Route::prefix('analytics')->name('analytics.')->group(function () {
        // 用户行为分析
        Route::get('behavior', [\App\Http\Controllers\Api\AnalyticsController::class, 'getBehaviorAnalysis']);
        Route::get('behavior/events', [\App\Http\Controllers\Api\AnalyticsController::class, 'getEventAnalysis']);
        Route::get('behavior/pages', [\App\Http\Controllers\Api\AnalyticsController::class, 'getPageAnalysis']);
        Route::get('behavior/flows', [\App\Http\Controllers\Api\AnalyticsController::class, 'getUserFlows']);
        
        // 用户留存分析
        Route::get('retention', [\App\Http\Controllers\Api\AnalyticsController::class, 'getRetentionAnalysis']);
        Route::get('retention/cohorts', [\App\Http\Controllers\Api\AnalyticsController::class, 'getCohortAnalysis']);
        
        // 用户细分分析
        Route::get('segments', [\App\Http\Controllers\Api\AnalyticsController::class, 'getUserSegments']);
        Route::post('segments', [\App\Http\Controllers\Api\AnalyticsController::class, 'createSegment']);
        Route::get('segments/{id}', [\App\Http\Controllers\Api\AnalyticsController::class, 'getSegmentDetail']);
        Route::put('segments/{id}', [\App\Http\Controllers\Api\AnalyticsController::class, 'updateSegment']);
        Route::delete('segments/{id}', [\App\Http\Controllers\Api\AnalyticsController::class, 'deleteSegment']);
        
        // 漏斗分析
        Route::get('funnel', [\App\Http\Controllers\Api\AnalyticsController::class, 'getFunnelAnalysis']);
        Route::post('funnel', [\App\Http\Controllers\Api\AnalyticsController::class, 'createFunnel']);
        
        // 实时分析
        Route::get('realtime', [\App\Http\Controllers\Api\AnalyticsController::class, 'getRealtimeAnalysis']);
        Route::get('realtime/visitors', [\App\Http\Controllers\Api\AnalyticsController::class, 'getRealtimeVisitors']);
        Route::get('realtime/events', [\App\Http\Controllers\Api\AnalyticsController::class, 'getRealtimeEvents']);
        
        // 用户生命周期和价值分析
        Route::get('lifecycle', [\App\Http\Controllers\Api\AnalyticsController::class, 'getLifecycleAnalysis']);
        Route::get('value', [\App\Http\Controllers\Api\AnalyticsController::class, 'getUserValueAnalysis']);
        
        // 地理和设备分析
        Route::get('geography', [\App\Http\Controllers\Api\AnalyticsController::class, 'getGeographyAnalysis']);
        Route::get('device', [\App\Http\Controllers\Api\AnalyticsController::class, 'getDeviceAnalysis']);
        
        // 自定义报表和导出
        Route::post('custom-report', [\App\Http\Controllers\Api\AnalyticsController::class, 'generateCustomReport']);
        Route::get('dimensions', [\App\Http\Controllers\Api\AnalyticsController::class, 'getDimensions']);
        Route::get('users', [\App\Http\Controllers\Api\AnalyticsController::class, 'getUserList']);
        Route::get('users/{userId}', [\App\Http\Controllers\Api\AnalyticsController::class, 'getUserDetail']);
    });

    // === 导航管理路由组 ===
    Route::prefix('navigation')->name('navigation.')->group(function () {
        // 导航偏好设置
        Route::get('preferences', [\App\Http\Controllers\Api\NavigationController::class, 'getPreferences']);
        Route::post('preferences', [\App\Http\Controllers\Api\NavigationController::class, 'updatePreferences']);
        Route::delete('preferences', [\App\Http\Controllers\Api\NavigationController::class, 'resetPreferences']);
        
        // 导航分析
        Route::get('analytics', [\App\Http\Controllers\Api\NavigationController::class, 'getAnalytics']);
        Route::get('analytics/overview', [\App\Http\Controllers\Api\NavigationController::class, 'getOverview']);
        Route::get('analytics/popular-pages', [\App\Http\Controllers\Api\NavigationController::class, 'getPopularPages']);
        Route::get('analytics/user-paths', [\App\Http\Controllers\Api\NavigationController::class, 'getUserPaths']);
        Route::get('analytics/time-distribution', [\App\Http\Controllers\Api\NavigationController::class, 'getTimeDistribution']);
        
        // 导航搜索和推荐
        Route::get('search', [\App\Http\Controllers\Api\NavigationController::class, 'search']);
        Route::get('recommendations', [\App\Http\Controllers\Api\NavigationController::class, 'getRecommendations']);
        Route::get('user-stats', [\App\Http\Controllers\Api\NavigationController::class, 'getUserStats']);
        
        // 导航配置和访问记录
        Route::get('/', [\App\Http\Controllers\Api\NavigationController::class, 'getNavigation']);
        Route::get('domains', [\App\Http\Controllers\Api\NavigationController::class, 'getDomains']);
        Route::post('visit', [\App\Http\Controllers\Api\NavigationController::class, 'recordVisit']);
    });

    // 分站管理路由（管理员专用）
    Route::prefix('substations')->group(function () {
        Route::get('/', [SubstationController::class, 'index']);
        Route::post('/', [SubstationController::class, 'store']);
        Route::get('{id}', [SubstationController::class, 'show']);
        Route::put('{id}', [SubstationController::class, 'update']);
        Route::delete('{id}', [SubstationController::class, 'destroy']);
        Route::put('{id}/status', [SubstationController::class, 'updateStatus']);
        Route::post('{id}/renew', [SubstationController::class, 'renew']);
        Route::get('{id}/health', [SubstationController::class, 'getHealth']);
        Route::get('stats', [SubstationController::class, 'getStats']);
        Route::post('batch-update', [SubstationController::class, 'batchUpdate']);
        Route::post('create-from-template', [SubstationController::class, 'createFromTemplate']);
        Route::post('export', [SubstationController::class, 'export']);
        Route::post('clear-cache', [SubstationController::class, 'clearCache']);

        // 分站财务管理
        Route::get('finance/stats', [SubstationController::class, 'getFinanceStats']);
        Route::get('finance/settlements', [SubstationController::class, 'getSettlementRecords']);
    });
});

// 文件上传路由
Route::middleware(['auth:api'])->group(function () {
    Route::post('upload', [\App\Http\Controllers\Api\UploadController::class, 'upload']);
    Route::post('upload/batch', [\App\Http\Controllers\Api\UploadController::class, 'batchUpload']);
    Route::delete('upload', [\App\Http\Controllers\Api\UploadController::class, 'delete']);
    Route::get('upload/info', [\App\Http\Controllers\Api\UploadController::class, 'info']);
});

// 公共API路由
Route::prefix('common')->group(function () {
    Route::get('cities', function () {
        return response()->json([
            'code' => 200,
            'message' => '获取城市列表成功',
            'data' => [
                ['code' => 'beijing', 'name' => '北京'],
                ['code' => 'shanghai', 'name' => '上海'],
                ['code' => 'guangzhou', 'name' => '广州'],
                ['code' => 'shenzhen', 'name' => '深圳'],
                ['code' => 'hangzhou', 'name' => '杭州'],
                ['code' => 'nanjing', 'name' => '南京'],
                ['code' => 'suzhou', 'name' => '苏州'],
                ['code' => 'wuhan', 'name' => '武汉'],
                ['code' => 'chengdu', 'name' => '成都'],
                ['code' => 'chongqing', 'name' => '重庆']
            ]
        ]);
    });
});

// 模板管理路由（无需认证）
Route::prefix('templates')->group(function () {
    Route::get('visible', function() {
        return response()->json([
            'code' => 200,
            'message' => '获取成功',
            'data' => [
                [
                    'id' => 1,
                    'name' => '欢迎新用户模板',
                    'description' => '用于欢迎新用户加入群组的模板',
                    'content' => '🎉 欢迎 {username} 加入我们的群组！',
                    'category' => 'welcome',
                    'is_active' => true,
                    'approval_status' => 'approved',
                    'usage_count' => 156,
                    'created_at' => '2024-01-15 10:30:00',
                    'updated_at' => '2024-12-01 14:20:00'
                ],
                [
                    'id' => 2,
                    'name' => '产品推广模板',
                    'description' => '用于推广产品的营销模板',
                    'content' => '🔥 限时优惠！{product_name}',
                    'category' => 'marketing',
                    'is_active' => true,
                    'approval_status' => 'approved',
                    'usage_count' => 89,
                    'created_at' => '2024-02-20 09:15:00',
                    'updated_at' => '2024-11-28 16:45:00'
                ],
                [
                    'id' => 3,
                    'name' => '活动通知模板',
                    'description' => '用于发布活动通知的模板',
                    'content' => '📢 活动通知\n\n🎯 活动主题：{event_title}',
                    'category' => 'event',
                    'is_active' => true,
                    'approval_status' => 'pending',
                    'usage_count' => 67,
                    'created_at' => '2024-03-10 11:00:00',
                    'updated_at' => '2024-12-05 10:30:00'
                ],
                [
                    'id' => 4,
                    'name' => '客服回复模板',
                    'description' => '客服常用的标准回复模板',
                    'content' => '您好！感谢您的咨询 😊',
                    'category' => 'service',
                    'is_active' => true,
                    'approval_status' => 'rejected',
                    'usage_count' => 234,
                    'created_at' => '2024-01-05 14:20:00',
                    'updated_at' => '2024-12-10 08:15:00'
                ]
            ]
        ]);
    });
});

// 冲突的头像管理路由已移除 - 使用第459行的真实控制器路由